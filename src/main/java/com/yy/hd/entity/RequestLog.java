package com.yy.hd.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 请求日志实体类
 * <AUTHOR> 2025/6/13
 */
@Data
public class RequestLog {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 请求ID（用于追踪）
     */
    private String requestId;
    
    /**
     * 用户邮箱
     */
    private String userEmail;
    
    /**
     * 用户passport
     */
    private String userPassport;
    
    /**
     * 用户YY号
     */
    private String userYy;
    
    /**
     * 请求URI
     */
    private String requestUri;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 原始图片大小（字节）
     */
    private Long imageOriginalSize;
    
    /**
     * 压缩后图片大小（字节）
     */
    private Long imageCompressedSize;
    
    /**
     * 压缩方法：tinypng, imageutil, iloveapi
     */
    private String compressionMethod;
    
    /**
     * 压缩比例（百分比）
     */
    private BigDecimal compressionRatio;
    
    /**
     * 处理时间（毫秒）
     */
    private Long processingTimeMs;
    
    /**
     * 是否成功：1-成功，0-失败
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
