package com.yy.hd.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * API Key使用记录实体类
 * <AUTHOR> 2025/6/13
 */
@Data
public class ApiKeyUsage {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * API Key的哈希值（用于标识，不存储原始key）
     */
    private String apiKeyHash;
    
    /**
     * 服务类型：tinypng, iloveapi
     */
    private String serviceType;
    
    /**
     * 使用月份，格式：YYYY-MM
     */
    private String usageMonth;
    
    /**
     * 使用次数
     */
    private Integer usageCount;
    
    /**
     * 最后使用时间
     */
    private LocalDateTime lastUsedTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
