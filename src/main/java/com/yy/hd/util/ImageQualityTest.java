package com.yy.hd.util;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * 图片质量测试类
 * 用于分析压缩前后的图片质量和光影效果
 */
public class ImageQualityTest {
    
    public static void main(String[] args) {
        try {
            String beforePath = "src/main/java/com/yy/hd/util/before.png";
            String afterOriginalPath = "src/main/java/com/yy/hd/util/after.png";
            String afterImprovedPath = "src/main/java/com/yy/hd/util/after.png";
            
            System.out.println("=== 图片压缩质量分析 ===");
            
            // 读取原图
            BufferedImage originalImage = ImageIO.read(new File(beforePath));
            System.out.println("原图尺寸: " + originalImage.getWidth() + "x" + originalImage.getHeight());
            System.out.println("原图类型: " + getImageTypeString(originalImage.getType()));
            System.out.println("原图位深度: " + originalImage.getColorModel().getPixelSize());
            
            // 使用改进的算法压缩
            System.out.println("\n--- 使用改进算法压缩 ---");
            FileInputStream input = new FileInputStream(beforePath);
            FileOutputStream output = new FileOutputStream(afterImprovedPath);
            
            long startTime = System.currentTimeMillis();
            ImageUtil.compress(input, output);
            long endTime = System.currentTimeMillis();
            
            input.close();
            output.close();
            
            System.out.println("压缩耗时: " + (endTime - startTime) + "ms");
            
            // 比较文件大小
            File beforeFile = new File(beforePath);
            File afterOriginalFile = new File(afterOriginalPath);
            File afterImprovedFile = new File(afterImprovedPath);
            
            System.out.println("\n=== 文件大小对比 ===");
            System.out.println("原图大小: " + formatFileSize(beforeFile.length()));
            
            if (afterOriginalFile.exists()) {
                System.out.println("旧算法压缩后: " + formatFileSize(afterOriginalFile.length()) + 
                    " (压缩率: " + String.format("%.2f%%", 
                    (1.0 - (double)afterOriginalFile.length() / beforeFile.length()) * 100) + ")");
            }
            
            System.out.println("新算法压缩后: " + formatFileSize(afterImprovedFile.length()) + 
                " (压缩率: " + String.format("%.2f%%", 
                (1.0 - (double)afterImprovedFile.length() / beforeFile.length()) * 100) + ")");
            
            // 分析图片质量
            System.out.println("\n=== 图片质量分析 ===");
            BufferedImage improvedImage = ImageIO.read(new File(afterImprovedPath));
            analyzeImageQuality(originalImage, improvedImage);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static String getImageTypeString(int type) {
        switch (type) {
            case BufferedImage.TYPE_INT_RGB: return "INT_RGB";
            case BufferedImage.TYPE_INT_ARGB: return "INT_ARGB";
            case BufferedImage.TYPE_INT_ARGB_PRE: return "INT_ARGB_PRE";
            case BufferedImage.TYPE_INT_BGR: return "INT_BGR";
            case BufferedImage.TYPE_3BYTE_BGR: return "3BYTE_BGR";
            case BufferedImage.TYPE_4BYTE_ABGR: return "4BYTE_ABGR";
            case BufferedImage.TYPE_4BYTE_ABGR_PRE: return "4BYTE_ABGR_PRE";
            case BufferedImage.TYPE_BYTE_GRAY: return "BYTE_GRAY";
            case BufferedImage.TYPE_USHORT_GRAY: return "USHORT_GRAY";
            case BufferedImage.TYPE_BYTE_BINARY: return "BYTE_BINARY";
            case BufferedImage.TYPE_BYTE_INDEXED: return "BYTE_INDEXED";
            case BufferedImage.TYPE_USHORT_565_RGB: return "USHORT_565_RGB";
            case BufferedImage.TYPE_USHORT_555_RGB: return "USHORT_555_RGB";
            default: return "CUSTOM(" + type + ")";
        }
    }
    
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
    }
    
    private static void analyzeImageQuality(BufferedImage original, BufferedImage compressed) {
        int width = original.getWidth();
        int height = original.getHeight();
        
        // 计算PSNR (Peak Signal-to-Noise Ratio)
        double mse = 0;
        int totalPixels = width * height;
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int originalRgb = original.getRGB(x, y);
                int compressedRgb = compressed.getRGB(x, y);
                
                int origR = (originalRgb >> 16) & 0xFF;
                int origG = (originalRgb >> 8) & 0xFF;
                int origB = originalRgb & 0xFF;
                
                int compR = (compressedRgb >> 16) & 0xFF;
                int compG = (compressedRgb >> 8) & 0xFF;
                int compB = compressedRgb & 0xFF;
                
                mse += Math.pow(origR - compR, 2) + Math.pow(origG - compG, 2) + Math.pow(origB - compB, 2);
            }
        }
        
        mse /= (totalPixels * 3); // 3个颜色通道
        
        if (mse == 0) {
            System.out.println("PSNR: 无穷大 (完全相同)");
        } else {
            double psnr = 20 * Math.log10(255.0 / Math.sqrt(mse));
            System.out.println("PSNR: " + String.format("%.2f dB", psnr));
            
            if (psnr > 40) {
                System.out.println("图片质量: 优秀");
            } else if (psnr > 30) {
                System.out.println("图片质量: 良好");
            } else if (psnr > 20) {
                System.out.println("图片质量: 一般");
            } else {
                System.out.println("图片质量: 较差");
            }
        }
        
        // 分析颜色数量
        System.out.println("压缩后图片类型: " + getImageTypeString(compressed.getType()));
        System.out.println("压缩后位深度: " + compressed.getColorModel().getPixelSize());
        
        if (compressed.getType() == BufferedImage.TYPE_BYTE_INDEXED) {
            java.awt.image.IndexColorModel icm = (java.awt.image.IndexColorModel) compressed.getColorModel();
            System.out.println("调色板大小: " + icm.getMapSize() + " 色");
        }
    }
}
