package com.yy.hd.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.awt.image.IndexColorModel;
import java.io.*;
import java.util.*;
import java.util.Random;

/**
 * 图片压缩工具类
 * 支持缓存功能的实例方法和静态方法
 * <AUTHOR> 2025/5/6
 */
@Component
public class ImageUtil {

    private static final Logger logger = LoggerFactory.getLogger(ImageUtil.class);

    @Autowired
    private ImageCacheManager cacheManager;

    /**
     * 带缓存的图片压缩方法（实例方法）
     * @param input 输入流
     * @param output 输出流
     * @throws IOException IO异常
     */
    public void compressWithCache(InputStream input, OutputStream output) throws IOException {
        // 读取输入数据
        byte[] inputData = input.readAllBytes();

        // 生成缓存键
        String cacheKey = "imageutil_" + cacheManager.generateCacheKey(inputData);

        // 检查缓存
        byte[] cachedData = cacheManager.readFromCache(cacheKey);
        if (cachedData != null) {
            logger.info("使用缓存的ImageUtil压缩图片: {}", cacheKey);
            output.write(cachedData);
            return;
        }

        // 缓存不存在，进行压缩
        logger.info("调用ImageUtil压缩图片: {}", cacheKey);

        ByteArrayOutputStream tempOutput = new ByteArrayOutputStream();
        compressInternal(new ByteArrayInputStream(inputData), tempOutput);
        byte[] compressedData = tempOutput.toByteArray();

        // 检查压缩效果，只有压缩后更小才缓存和使用
        if (compressedData.length < inputData.length) {
            // 写入缓存
            cacheManager.writeToCache(cacheKey, compressedData);

            // 输出压缩后的数据
            output.write(compressedData);

            logger.info("ImageUtil压缩成功: {} -> {} 字节", inputData.length, compressedData.length);
        } else {
            // 压缩后反而更大，使用原图
            logger.warn("ImageUtil压缩后文件更大，使用原图: {} vs {} 字节", inputData.length, compressedData.length);
            output.write(inputData);
        }
    }

    /**
     * 静态压缩方法（保持向后兼容）
     * @param input 输入流
     * @param output 输出流
     */
    public static void compress(InputStream input, OutputStream output) {
        try {
            // 静态方法调用内部压缩逻辑，但不使用缓存
            compressInternal(input, output);
        } catch (IOException e) {
            logger.error("ImageUtil静态压缩失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 内部压缩逻辑（静态方法，供实例方法和静态方法共用）
     * @param input 输入流
     * @param output 输出流
     * @throws IOException IO异常
     */
    private static void compressInternal(InputStream input, OutputStream output) throws IOException {
        // 将输入流转换为字节数组，以便多次读取
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        int bytesRead;
        while ((bytesRead = input.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        byte[] originalData = baos.toByteArray();
        long originalSize = originalData.length;

        // 读取输入图片
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(originalData));

        if (image == null) {
            throw new IOException("无法读取图片");
        }

        // 获取图片位深度
        int bitDepth = image.getColorModel().getPixelSize();
        int imageType = image.getType();

        logger.debug("原图信息: 位深度={}, 类型={}, 大小={}字节", bitDepth, imageType, originalSize);

        // 检查图片类型和位深度
        if (bitDepth <= 8 || imageType == BufferedImage.TYPE_BYTE_INDEXED) {
            // 对于8位或更低位深度的图片，尝试优化压缩
            logger.debug("原图已经是{}位深度，尝试优化压缩", bitDepth);

            byte[] compressedData = optimizeCompression(image, originalData, originalSize);
            output.write(compressedData);
        } else {
            // 只有当图片不是索引色模式且位深度大于8时才进行转换
            logger.debug("原图是{}位深度，进行8位转换压缩", bitDepth);
            BufferedImage indexedImage = convertTo8Bit(image);

            // 保存转换后的图片并检查大小
            ByteArrayOutputStream tempOutput = new ByteArrayOutputStream();
            ImageIO.write(indexedImage, "png", tempOutput);
            byte[] compressedData = tempOutput.toByteArray();

            if (compressedData.length >= originalSize) {
                logger.warn("压缩后大小({})不小于原图({})，使用原图", compressedData.length, originalSize);
                output.write(originalData);
            } else {
                logger.info("压缩成功: {} -> {} 字节", originalSize, compressedData.length);
                output.write(compressedData);
            }
        }
    }

    /**
     * 优化8位图片的压缩
     */
    private static byte[] optimizeCompression(BufferedImage image, byte[] originalData, long originalSize) throws IOException {
        // 尝试多种压缩策略，选择最小的结果
        List<byte[]> candidates = new ArrayList<>();

        // 策略1: 直接使用原始数据（如果原始格式已经很好）
        candidates.add(originalData);

        // 策略2: PNG压缩（适合有透明度或颜色较少的图片）
        try {
            ByteArrayOutputStream pngOutput = new ByteArrayOutputStream();
            ImageIO.write(image, "png", pngOutput);
            candidates.add(pngOutput.toByteArray());
        } catch (Exception e) {
            logger.debug("PNG压缩失败: {}", e.getMessage());
        }

        // 策略3: JPEG压缩（适合照片类图片，但会丢失透明度）
        if (!image.getColorModel().hasAlpha()) {
            try {
                ByteArrayOutputStream jpegOutput = new ByteArrayOutputStream();
                // 创建RGB图像（JPEG不支持透明度）
                BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);
                rgbImage.getGraphics().drawImage(image, 0, 0, null);

                ImageWriter jpegWriter = ImageIO.getImageWritersByFormatName("jpeg").next();
                ImageWriteParam jpegParams = jpegWriter.getDefaultWriteParam();
                jpegParams.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                jpegParams.setCompressionQuality(0.85f); // 85%质量

                ImageOutputStream ios = ImageIO.createImageOutputStream(jpegOutput);
                jpegWriter.setOutput(ios);
                jpegWriter.write(null, new javax.imageio.IIOImage(rgbImage, null, null), jpegParams);
                jpegWriter.dispose();
                ios.close();

                candidates.add(jpegOutput.toByteArray());
            } catch (Exception e) {
                logger.debug("JPEG压缩失败: {}", e.getMessage());
            }
        }

        // 策略4: 如果是索引色图片，尝试重新优化调色板
        if (image.getType() == BufferedImage.TYPE_BYTE_INDEXED) {
            try {
                BufferedImage optimizedIndexed = optimizeIndexedImage(image);
                ByteArrayOutputStream indexedOutput = new ByteArrayOutputStream();
                ImageIO.write(optimizedIndexed, "png", indexedOutput);
                candidates.add(indexedOutput.toByteArray());
            } catch (Exception e) {
                logger.debug("索引色优化失败: {}", e.getMessage());
            }
        }

        // 选择最小的结果
        byte[] bestResult = originalData;
        long bestSize = originalSize;
        String bestMethod = "原始";

        for (int i = 0; i < candidates.size(); i++) {
            byte[] candidate = candidates.get(i);
            if (candidate.length < bestSize) {
                bestResult = candidate;
                bestSize = candidate.length;
                switch (i) {
                    case 0: bestMethod = "原始"; break;
                    case 1: bestMethod = "PNG"; break;
                    case 2: bestMethod = "JPEG"; break;
                    case 3: bestMethod = "优化索引色"; break;
                }
            }
        }

        logger.debug("最佳压缩方法: {}, 大小: {} -> {} 字节", bestMethod, originalSize, bestSize);
        return bestResult;
    }

    /**
     * 优化索引色图片
     */
    private static BufferedImage optimizeIndexedImage(BufferedImage src) {
        // 如果已经是索引色，尝试减少调色板中的颜色数量
        IndexColorModel srcColorModel = (IndexColorModel) src.getColorModel();
        int mapSize = srcColorModel.getMapSize();

        // 统计实际使用的颜色
        Set<Integer> usedColors = new HashSet<>();
        for (int y = 0; y < src.getHeight(); y++) {
            for (int x = 0; x < src.getWidth(); x++) {
                usedColors.add(src.getRaster().getSample(x, y, 0));
            }
        }

        logger.debug("调色板大小: {}, 实际使用颜色: {}", mapSize, usedColors.size());

        // 如果实际使用的颜色远少于调色板大小，创建优化的调色板
        if (usedColors.size() < mapSize * 0.7) {
            List<Integer> usedColorList = new ArrayList<>(usedColors);
            Collections.sort(usedColorList);

            byte[] r = new byte[usedColorList.size()];
            byte[] g = new byte[usedColorList.size()];
            byte[] b = new byte[usedColorList.size()];
            byte[] a = new byte[usedColorList.size()];

            for (int i = 0; i < usedColorList.size(); i++) {
                int colorIndex = usedColorList.get(i);
                r[i] = (byte) srcColorModel.getRed(colorIndex);
                g[i] = (byte) srcColorModel.getGreen(colorIndex);
                b[i] = (byte) srcColorModel.getBlue(colorIndex);
                a[i] = (byte) srcColorModel.getAlpha(colorIndex);
            }

            IndexColorModel newColorModel = new IndexColorModel(8, usedColorList.size(), r, g, b, a);
            BufferedImage optimized = new BufferedImage(src.getWidth(), src.getHeight(),
                                                       BufferedImage.TYPE_BYTE_INDEXED, newColorModel);

            // 重新映射像素
            for (int y = 0; y < src.getHeight(); y++) {
                for (int x = 0; x < src.getWidth(); x++) {
                    int oldIndex = src.getRaster().getSample(x, y, 0);
                    int newIndex = usedColorList.indexOf(oldIndex);
                    optimized.getRaster().setSample(x, y, 0, newIndex);
                }
            }

            return optimized;
        }

        return src;
    }

    private static BufferedImage convertTo8Bit(BufferedImage src) {
        int width = src.getWidth();
        int height = src.getHeight();

        // 生成调色板，支持Alpha通道
        IndexColorModel colorModel = createIndexColorModel(src);
        BufferedImage indexedImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_INDEXED, colorModel);

        // 应用Floyd-Steinberg抖动
        applyFloydSteinbergDithering(src, indexedImage, colorModel);

        return indexedImage;
    }

    private static IndexColorModel createIndexColorModel(BufferedImage src) {
        // 使用K-means聚类生成更精确的调色板
        return createOptimalPalette(src, 256);
    }

    /**
     * 使用K-means聚类创建最优调色板
     */
    private static IndexColorModel createOptimalPalette(BufferedImage src, int maxColors) {
        int width = src.getWidth();
        int height = src.getHeight();
        boolean hasAlpha = src.getColorModel().hasAlpha();

        // 收集所有像素颜色
        List<double[]> pixels = new ArrayList<>();
        Map<String, Integer> colorFreq = new HashMap<>();

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgba = src.getRGB(x, y);
                int r = (rgba >> 16) & 0xFF;
                int g = (rgba >> 8) & 0xFF;
                int b = rgba & 0xFF;
                int a = (rgba >> 24) & 0xFF;

                // 跳过完全透明的像素
                if (hasAlpha && a <= 5) continue;

                // 转换到LAB色彩空间以获得更好的感知距离
                double[] lab = rgbToLab(r, g, b);
                pixels.add(new double[]{lab[0], lab[1], lab[2], a});

                String key = r + "," + g + "," + b + "," + a;
                colorFreq.put(key, colorFreq.getOrDefault(key, 0) + 1);
            }
        }

        if (pixels.isEmpty()) {
            // 如果没有有效像素，返回基本调色板
            return createBasicPalette();
        }

        // 使用K-means聚类生成调色板，为重要颜色保留更多空间
        int targetColors = Math.min(maxColors - 1, pixels.size());

        // 如果像素数量很大，先进行采样以提高性能
        if (pixels.size() > 50000) {
            pixels = samplePixels(pixels, 50000);
        }

        List<double[]> centers = kMeansClustering(pixels, targetColors);

        // 创建IndexColorModel
        byte[] r = new byte[maxColors];
        byte[] g = new byte[maxColors];
        byte[] b = new byte[maxColors];
        byte[] a = new byte[maxColors];

        // 第一个颜色是透明色
        r[0] = 0; g[0] = 0; b[0] = 0; a[0] = 0;

        // 添加聚类中心
        for (int i = 0; i < centers.size() && i < maxColors - 1; i++) {
            double[] center = centers.get(i);
            int[] rgb = labToRgb(center[0], center[1], center[2]);

            r[i + 1] = (byte) Math.max(0, Math.min(255, rgb[0]));
            g[i + 1] = (byte) Math.max(0, Math.min(255, rgb[1]));
            b[i + 1] = (byte) Math.max(0, Math.min(255, rgb[2]));
            a[i + 1] = (byte) Math.max(0, Math.min(255, (int) center[3]));
        }

        // 填充剩余位置
        for (int i = centers.size() + 1; i < maxColors; i++) {
            r[i] = 0; g[i] = 0; b[i] = 0; a[i] = (byte) 255;
        }

        return new IndexColorModel(8, maxColors, r, g, b, a);
    }

    /**
     * 对像素进行采样以提高性能
     */
    private static List<double[]> samplePixels(List<double[]> pixels, int maxSamples) {
        if (pixels.size() <= maxSamples) {
            return pixels;
        }

        Random random = new Random(42);
        List<double[]> sampled = new ArrayList<>();

        // 使用分层采样，确保颜色分布的代表性
        int step = pixels.size() / maxSamples;
        for (int i = 0; i < pixels.size(); i += step) {
            if (sampled.size() >= maxSamples) break;
            sampled.add(pixels.get(i));
        }

        // 如果还有空间，随机添加一些像素
        while (sampled.size() < maxSamples && sampled.size() < pixels.size()) {
            int randomIndex = random.nextInt(pixels.size());
            double[] pixel = pixels.get(randomIndex);

            // 避免重复
            boolean exists = false;
            for (double[] existing : sampled) {
                if (labDistance(pixel, existing) < 1.0) {
                    exists = true;
                    break;
                }
            }

            if (!exists) {
                sampled.add(pixel);
            }
        }

        return sampled;
    }

    /**
     * K-means聚类算法生成调色板
     */
    private static List<double[]> kMeansClustering(List<double[]> pixels, int k) {
        if (pixels.size() <= k) {
            return pixels;
        }

        Random random = new Random(42); // 固定种子以获得一致结果
        List<double[]> centers = new ArrayList<>();

        // 使用K-means++初始化
        centers.add(pixels.get(random.nextInt(pixels.size())).clone());

        for (int i = 1; i < k; i++) {
            double[] weights = new double[pixels.size()];
            double totalWeight = 0;

            for (int j = 0; j < pixels.size(); j++) {
                double minDist = Double.MAX_VALUE;
                for (double[] center : centers) {
                    double dist = labDistance(pixels.get(j), center);
                    minDist = Math.min(minDist, dist);
                }
                weights[j] = minDist * minDist;
                totalWeight += weights[j];
            }

            double r = random.nextDouble() * totalWeight;
            double sum = 0;
            for (int j = 0; j < pixels.size(); j++) {
                sum += weights[j];
                if (sum >= r) {
                    centers.add(pixels.get(j).clone());
                    break;
                }
            }
        }

        // 迭代优化
        for (int iter = 0; iter < 50; iter++) {
            List<List<double[]>> clusters = new ArrayList<>();
            for (int i = 0; i < k; i++) {
                clusters.add(new ArrayList<>());
            }

            // 分配像素到最近的中心
            for (double[] pixel : pixels) {
                int bestCenter = 0;
                double minDist = labDistance(pixel, centers.get(0));

                for (int i = 1; i < centers.size(); i++) {
                    double dist = labDistance(pixel, centers.get(i));
                    if (dist < minDist) {
                        minDist = dist;
                        bestCenter = i;
                    }
                }
                clusters.get(bestCenter).add(pixel);
            }

            // 更新中心
            boolean changed = false;
            for (int i = 0; i < centers.size(); i++) {
                if (!clusters.get(i).isEmpty()) {
                    double[] newCenter = calculateCentroid(clusters.get(i));
                    if (labDistance(centers.get(i), newCenter) > 1.0) {
                        centers.set(i, newCenter);
                        changed = true;
                    }
                }
            }

            if (!changed) break;
        }

        return centers;
    }

    /**
     * 计算聚类中心
     */
    private static double[] calculateCentroid(List<double[]> cluster) {
        double[] centroid = new double[4]; // L, a, b, alpha
        for (double[] pixel : cluster) {
            for (int i = 0; i < 4; i++) {
                centroid[i] += pixel[i];
            }
        }
        for (int i = 0; i < 4; i++) {
            centroid[i] /= cluster.size();
        }
        return centroid;
    }

    /**
     * 检测图像中的渐变区域
     */
    private static boolean[][] detectGradientAreas(BufferedImage src) {
        int width = src.getWidth();
        int height = src.getHeight();
        boolean[][] gradientAreas = new boolean[height][width];

        // 计算每个像素的颜色变化梯度
        for (int y = 1; y < height - 1; y++) {
            for (int x = 1; x < width - 1; x++) {
                int centerRgb = src.getRGB(x, y);
                int centerR = (centerRgb >> 16) & 0xFF;
                int centerG = (centerRgb >> 8) & 0xFF;
                int centerB = centerRgb & 0xFF;

                // 计算与周围8个像素的颜色差异
                double totalDiff = 0;
                int validNeighbors = 0;

                for (int dy = -1; dy <= 1; dy++) {
                    for (int dx = -1; dx <= 1; dx++) {
                        if (dx == 0 && dy == 0) continue;

                        int nx = x + dx;
                        int ny = y + dy;
                        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                            int neighborRgb = src.getRGB(nx, ny);
                            int neighborR = (neighborRgb >> 16) & 0xFF;
                            int neighborG = (neighborRgb >> 8) & 0xFF;
                            int neighborB = neighborRgb & 0xFF;

                            double diff = Math.sqrt(
                                Math.pow(centerR - neighborR, 2) +
                                Math.pow(centerG - neighborG, 2) +
                                Math.pow(centerB - neighborB, 2)
                            );
                            totalDiff += diff;
                            validNeighbors++;
                        }
                    }
                }

                double avgDiff = totalDiff / validNeighbors;

                // 如果平均颜色差异在一定范围内，认为是渐变区域
                // 不是完全相同（避免纯色区域），也不是差异太大（避免边缘）
                if (avgDiff > 5 && avgDiff < 30) {
                    gradientAreas[y][x] = true;
                }
            }
        }

        return gradientAreas;
    }

    /**
     * 改进的中值切分算法，更好地处理渐变
     */
    private static List<int[]> improvedMedianCut(List<int[]> colors, int desiredColors) {
        List<List<int[]>> buckets = new ArrayList<>();
        buckets.add(new ArrayList<>(colors));

        while (buckets.size() < desiredColors && !buckets.isEmpty()) {
            int maxRangeIndex = -1;
            double maxScore = -1;

            for (int i = 0; i < buckets.size(); i++) {
                List<int[]> bucket = buckets.get(i);
                if (bucket.size() <= 1) continue;

                int[] range = getColorRange(bucket);
                int rangeValue = Math.max(range[0], Math.max(range[1], range[2]));

                // 计算桶的总权重（像素数量）
                long totalWeight = bucket.stream().mapToLong(color -> color[4]).sum();

                // 综合考虑颜色范围和权重，优先分割高权重且高范围的桶
                double score = rangeValue * Math.log(totalWeight + 1);

                if (score > maxScore) {
                    maxScore = score;
                    maxRangeIndex = i;
                }
            }

            if (maxRangeIndex == -1) break;

            List<int[]> bucketToSplit = buckets.remove(maxRangeIndex);
            List<int[]>[] splitBuckets = improvedSplitBucket(bucketToSplit);
            buckets.add(splitBuckets[0]);
            buckets.add(splitBuckets[1]);
        }

        List<int[]> palette = new ArrayList<>();
        for (List<int[]> bucket : buckets) {
            if (!bucket.isEmpty()) {
                palette.add(getWeightedAverageColor(bucket));
            }
        }

        while (palette.size() < desiredColors) {
            palette.add(new int[]{0, 0, 0, 0}); // 填充透明
        }

        return palette.subList(0, Math.min(palette.size(), desiredColors));
    }

    private static List<int[]> medianCut(List<int[]> colors, int desiredColors) {
        List<List<int[]>> buckets = new ArrayList<>();
        buckets.add(new ArrayList<>(colors));

        while (buckets.size() < desiredColors && !buckets.isEmpty()) {
            int maxRangeIndex = -1;
            int maxRange = -1;
            for (int i = 0; i < buckets.size(); i++) {
                List<int[]> bucket = buckets.get(i);
                int[] range = getColorRange(bucket);
                int rangeValue = Math.max(range[0], Math.max(range[1], range[2]));
                if (rangeValue > maxRange) {
                    maxRange = rangeValue;
                    maxRangeIndex = i;
                }
            }

            if (maxRangeIndex == -1) break;

            List<int[]> bucketToSplit = buckets.remove(maxRangeIndex);
            List<int[]>[] splitBuckets = splitBucket(bucketToSplit);
            buckets.add(splitBuckets[0]);
            buckets.add(splitBuckets[1]);
        }

        List<int[]> palette = new ArrayList<>();
        for (List<int[]> bucket : buckets) {
            if (!bucket.isEmpty()) {
                palette.add(getAverageColor(bucket));
            }
        }

        while (palette.size() < desiredColors) {
            palette.add(new int[]{0, 0, 0, 0}); // 填充透明
        }

        return palette.subList(0, Math.min(palette.size(), desiredColors));
    }

    private static int[] getColorRange(List<int[]> bucket) {
        int minR = 255, maxR = 0;
        int minG = 255, maxG = 0;
        int minB = 255, maxB = 0;

        for (int[] color : bucket) {
            int r = color[0];
            int g = color[1];
            int b = color[2];
            minR = Math.min(minR, r);
            maxR = Math.max(maxR, r);
            minG = Math.min(minG, g);
            maxG = Math.max(maxG, g);
            minB = Math.min(minB, b);
            maxB = Math.max(maxB, b);
        }

        return new int[]{maxR - minR, maxG - minG, maxB - minB};
    }

    /**
     * 改进的桶分割方法，考虑权重分布
     */
    private static List<int[]>[] improvedSplitBucket(List<int[]> bucket) {
        int[] range = getColorRange(bucket);
        int channel = 0;
        if (range[1] > range[0]) channel = 1;
        if (range[2] > range[channel]) channel = 2;

        final int finalChannel = channel;
        bucket.sort((a, b) -> Integer.compare(a[finalChannel], b[finalChannel]));

        // 按权重分割，而不是简单的中点分割
        long totalWeight = bucket.stream().mapToLong(color -> color[4]).sum();
        long targetWeight = totalWeight / 2;

        long currentWeight = 0;
        int splitIndex = bucket.size() / 2; // 默认中点

        for (int i = 0; i < bucket.size(); i++) {
            currentWeight += bucket.get(i)[4];
            if (currentWeight >= targetWeight) {
                splitIndex = Math.max(1, Math.min(bucket.size() - 1, i));
                break;
            }
        }

        List<int[]> bucket1 = new ArrayList<>(bucket.subList(0, splitIndex));
        List<int[]> bucket2 = new ArrayList<>(bucket.subList(splitIndex, bucket.size()));

        return new List[]{bucket1, bucket2};
    }

    private static List<int[]>[] splitBucket(List<int[]> bucket) {
        int[] range = getColorRange(bucket);
        int channel = 0;
        if (range[1] > range[0]) channel = 1;
        if (range[2] > range[channel]) channel = 2;

        final int finalChannel = channel;
        bucket.sort((a, b) -> Integer.compare(a[finalChannel], b[finalChannel]));

        int mid = bucket.size() / 2;
        List<int[]> bucket1 = new ArrayList<>(bucket.subList(0, mid));
        List<int[]> bucket2 = new ArrayList<>(bucket.subList(mid, bucket.size()));

        return new List[]{bucket1, bucket2};
    }

    /**
     * 加权平均颜色计算，考虑颜色的重要性
     */
    private static int[] getWeightedAverageColor(List<int[]> bucket) {
        long sumR = 0, sumG = 0, sumB = 0, sumA = 0;
        long totalCount = 0;

        for (int[] color : bucket) {
            int count = color[4];
            sumR += color[0] * count;
            sumG += color[1] * count;
            sumB += color[2] * count;
            sumA += color[3] * count;
            totalCount += count;
        }

        if (totalCount == 0) return new int[]{0, 0, 0, 0};

        return new int[]{
                (int) (sumR / totalCount),
                (int) (sumG / totalCount),
                (int) (sumB / totalCount),
                (int) (sumA / totalCount)
        };
    }

    private static int[] getAverageColor(List<int[]> bucket) {
        long sumR = 0, sumG = 0, sumB = 0, sumA = 0;
        long totalCount = 0;

        for (int[] color : bucket) {
            int count = color[4];
            sumR += color[0] * count;
            sumG += color[1] * count;
            sumB += color[2] * count;
            sumA += color[3] * count;
            totalCount += count;
        }

        if (totalCount == 0) return new int[]{0, 0, 0, 0};

        return new int[]{
                (int) (sumR / totalCount),
                (int) (sumG / totalCount),
                (int) (sumB / totalCount),
                (int) (sumA / totalCount)
        };
    }

    private static void applyFloydSteinbergDithering(BufferedImage src, BufferedImage dest, IndexColorModel colorModel) {
        int width = src.getWidth();
        int height = src.getHeight();
        boolean hasAlpha = src.getColorModel().hasAlpha();

        // 预处理图像，检测边缘透明区域和渐变区域
        boolean[][] isTransparentOrBorder = new boolean[height][width];
        boolean[][] isGradientArea = detectGradientAreas(src);

        // 首先标记透明像素
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int srcPixel = src.getRGB(x, y);
                int srcA = (srcPixel >> 24) & 0xFF;

                // 如果是完全透明的像素或者接近透明
                if (hasAlpha && srcA <= 5) {
                    isTransparentOrBorder[y][x] = true;
                }
            }
        }

        // 标记边缘区域（与透明像素相邻的非透明像素）
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                if (!isTransparentOrBorder[y][x]) {
                    // 检查周围是否有透明像素
                    boolean isBorder = false;
                    for (int dy = -1; dy <= 1 && !isBorder; dy++) {
                        for (int dx = -1; dx <= 1 && !isBorder; dx++) {
                            int nx = x + dx;
                            int ny = y + dy;
                            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                                if (isTransparentOrBorder[ny][nx]) {
                                    isBorder = true;
                                }
                            }
                        }
                    }

                    // 如果是边缘像素，标记为特殊处理
                    if (isBorder) {
                        isTransparentOrBorder[y][x] = true;
                    }
                }
            }
        }

        // 遍历每个像素
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int srcPixel = src.getRGB(x, y);
                int srcR = (srcPixel >> 16) & 0xFF;
                int srcG = (srcPixel >> 8) & 0xFF;
                int srcB = srcPixel & 0xFF;
                int srcA = (srcPixel >> 24) & 0xFF;

                // 对于透明像素和边缘像素，使用特殊处理
                if (isTransparentOrBorder[y][x]) {
                    // 对于透明像素，直接使用完全透明的调色板索引
                    if (srcA <= 5) {
                        // 直接使用索引0（我们确保它是完全透明的）
                        dest.getRaster().setPixel(x, y, new int[]{0});
                        continue;
                    } else {
                        // 对于边缘像素，不进行扩散，直接使用最接近的颜色
                        int index = findClosestColor(srcR, srcG, srcB, srcA, colorModel);
                        dest.getRaster().setPixel(x, y, new int[]{index});
                        continue;
                    }
                }

                // 对于渐变区域，使用Sierra抖动算法
                if (isGradientArea[y][x]) {
                    applySierraDithering(src, dest, colorModel, x, y, isTransparentOrBorder);
                } else {
                    // 对于普通像素，使用标准Floyd-Steinberg扩散
                    int index = findClosestColor(srcR, srcG, srcB, srcA, colorModel);
                    int destR = colorModel.getRed(index);
                    int destG = colorModel.getGreen(index);
                    int destB = colorModel.getBlue(index);
                    int destA = colorModel.getAlpha(index);

                    // 设置目标像素
                    dest.getRaster().setPixel(x, y, new int[]{index});

                    // 计算量化误差
                    int errR = srcR - destR;
                    int errG = srcG - destG;
                    int errB = srcB - destB;
                    int errA = srcA - destA;

                    // Floyd-Steinberg扩散，但不扩散到边缘或透明区域
                    if (x + 1 < width && !isTransparentOrBorder[y][x + 1]) {
                        distributeError(src, x + 1, y, errR, errG, errB, errA, 7.0 / 16.0);
                    }
                    if (x - 1 >= 0 && y + 1 < height && !isTransparentOrBorder[y + 1][x - 1]) {
                        distributeError(src, x - 1, y + 1, errR, errG, errB, errA, 3.0 / 16.0);
                    }
                    if (y + 1 < height && !isTransparentOrBorder[y + 1][x]) {
                        distributeError(src, x, y + 1, errR, errG, errB, errA, 5.0 / 16.0);
                    }
                    if (x + 1 < width && y + 1 < height && !isTransparentOrBorder[y + 1][x + 1]) {
                        distributeError(src, x + 1, y + 1, errR, errG, errB, errA, 1.0 / 16.0);
                    }
                }
            }
        }
    }

    /**
     * Sierra抖动算法，专门用于渐变区域
     * 提供更好的渐变质量和更少的条带化
     */
    private static void applySierraDithering(BufferedImage src, BufferedImage dest, IndexColorModel colorModel,
                                           int x, int y, boolean[][] isTransparentOrBorder) {
        int width = src.getWidth();
        int height = src.getHeight();

        int srcPixel = src.getRGB(x, y);
        int srcR = (srcPixel >> 16) & 0xFF;
        int srcG = (srcPixel >> 8) & 0xFF;
        int srcB = srcPixel & 0xFF;
        int srcA = (srcPixel >> 24) & 0xFF;

        // 寻找最接近的颜色
        int index = findClosestColor(srcR, srcG, srcB, srcA, colorModel);
        int destR = colorModel.getRed(index);
        int destG = colorModel.getGreen(index);
        int destB = colorModel.getBlue(index);
        int destA = colorModel.getAlpha(index);

        // 设置目标像素
        dest.getRaster().setPixel(x, y, new int[]{index});

        // 计算量化误差
        int errR = srcR - destR;
        int errG = srcG - destG;
        int errB = srcB - destB;
        int errA = srcA - destA;

        // Sierra抖动矩阵 (更好的渐变效果)
        int[][] sierraOffsets = {
            {1, 0, 5}, {2, 0, 3},
            {-2, 1, 2}, {-1, 1, 4}, {0, 1, 5}, {1, 1, 4}, {2, 1, 2},
            {-1, 2, 2}, {0, 2, 3}, {1, 2, 2}
        };

        for (int[] offset : sierraOffsets) {
            int nx = x + offset[0];
            int ny = y + offset[1];
            double weight = offset[2] / 32.0;

            if (nx >= 0 && nx < width && ny >= 0 && ny < height &&
                !isTransparentOrBorder[ny][nx]) {
                distributeError(src, nx, ny, errR, errG, errB, errA, weight);
            }
        }
    }

    /**
     * 改进的抖动算法，专门用于渐变区域
     * 使用更精细的误差扩散模式来减少条带化
     */
    private static void applyImprovedDithering(BufferedImage src, BufferedImage dest, IndexColorModel colorModel,
                                             int x, int y, boolean[][] isTransparentOrBorder) {
        int width = src.getWidth();
        int height = src.getHeight();

        int srcPixel = src.getRGB(x, y);
        int srcR = (srcPixel >> 16) & 0xFF;
        int srcG = (srcPixel >> 8) & 0xFF;
        int srcB = srcPixel & 0xFF;
        int srcA = (srcPixel >> 24) & 0xFF;

        // 寻找最接近的颜色
        int index = findClosestColor(srcR, srcG, srcB, srcA, colorModel);
        int destR = colorModel.getRed(index);
        int destG = colorModel.getGreen(index);
        int destB = colorModel.getBlue(index);
        int destA = colorModel.getAlpha(index);

        // 设置目标像素
        dest.getRaster().setPixel(x, y, new int[]{index});

        // 计算量化误差
        int errR = srcR - destR;
        int errG = srcG - destG;
        int errB = srcB - destB;
        int errA = srcA - destA;

        // 使用改进的Atkinson抖动模式，减少条带化
        // Atkinson抖动在渐变区域表现更好
        double[][] atkinsonMatrix = {
            {0, 0, 1.0/8.0, 1.0/8.0},
            {1.0/8.0, 1.0/8.0, 1.0/8.0, 0},
            {0, 1.0/8.0, 0, 0}
        };

        for (int dy = 0; dy < 3; dy++) {
            for (int dx = -1; dx < 3; dx++) {
                if (dy == 0 && dx <= 0) continue; // 跳过当前像素和已处理的像素

                int nx = x + dx;
                int ny = y + dy;

                if (nx >= 0 && nx < width && ny >= 0 && ny < height &&
                    !isTransparentOrBorder[ny][nx]) {

                    double weight = atkinsonMatrix[dy][dx + 1];
                    if (weight > 0) {
                        distributeError(src, nx, ny, errR, errG, errB, errA, weight);
                    }
                }
            }
        }
    }

    // 找到调色板中完全透明的颜色索引
    private static int findTransparentColorIndex(IndexColorModel colorModel) {
        for (int i = 0; i < colorModel.getMapSize(); i++) {
            if (colorModel.getAlpha(i) == 0) {
                return i;
            }
        }
        // 如果没有完全透明的颜色，返回0
        return 0;
    }

    private static void distributeError(BufferedImage img, int x, int y, int errR, int errG, int errB, int errA, double weight) {
        int pixel = img.getRGB(x, y);
        int r = clamp(((pixel >> 16) & 0xFF) + (int) (errR * weight), 0, 255);
        int g = clamp(((pixel >> 8) & 0xFF) + (int) (errG * weight), 0, 255);
        int b = clamp((pixel & 0xFF) + (int) (errB * weight), 0, 255);
        int a = clamp(((pixel >> 24) & 0xFF) + (int) (errA * weight), 0, 255);
        img.setRGB(x, y, (a << 24) | (r << 16) | (g << 8) | b);
    }

    private static int findClosestColor(int r, int g, int b, int a, IndexColorModel colorModel) {
        // 如果输入像素接近透明，直接返回透明色索引
        if (a <= 5) {
            return 0; // 索引0是我们保证的透明色
        }

        // 转换到LAB色彩空间进行更精确的颜色匹配
        double[] inputLab = rgbToLab(r, g, b);
        double[] inputLabA = {inputLab[0], inputLab[1], inputLab[2], a};

        double minDist = Double.MAX_VALUE;
        int closestIndex = 1; // 从索引1开始，跳过透明色

        for (int i = 1; i < colorModel.getMapSize(); i++) {
            int paletteR = colorModel.getRed(i);
            int paletteG = colorModel.getGreen(i);
            int paletteB = colorModel.getBlue(i);
            int paletteA = colorModel.getAlpha(i);

            // 转换调色板颜色到LAB空间
            double[] paletteLab = rgbToLab(paletteR, paletteG, paletteB);
            double[] paletteLabA = {paletteLab[0], paletteLab[1], paletteLab[2], paletteA};

            // 使用感知色彩距离
            double dist = labDistance(inputLabA, paletteLabA);

            if (dist < minDist) {
                minDist = dist;
                closestIndex = i;
            }
        }

        return closestIndex;
    }

    /**
     * RGB转LAB色彩空间
     */
    private static double[] rgbToLab(int r, int g, int b) {
        // 首先转换到XYZ
        double[] xyz = rgbToXyz(r, g, b);
        return xyzToLab(xyz[0], xyz[1], xyz[2]);
    }

    /**
     * LAB转RGB色彩空间
     */
    private static int[] labToRgb(double l, double a, double b) {
        double[] xyz = labToXyz(l, a, b);
        return xyzToRgb(xyz[0], xyz[1], xyz[2]);
    }

    /**
     * RGB转XYZ色彩空间
     */
    private static double[] rgbToXyz(int r, int g, int b) {
        double rNorm = r / 255.0;
        double gNorm = g / 255.0;
        double bNorm = b / 255.0;

        // Gamma校正
        rNorm = (rNorm > 0.04045) ? Math.pow((rNorm + 0.055) / 1.055, 2.4) : rNorm / 12.92;
        gNorm = (gNorm > 0.04045) ? Math.pow((gNorm + 0.055) / 1.055, 2.4) : gNorm / 12.92;
        bNorm = (bNorm > 0.04045) ? Math.pow((bNorm + 0.055) / 1.055, 2.4) : bNorm / 12.92;

        // 转换到XYZ (使用D65白点)
        double x = rNorm * 0.4124564 + gNorm * 0.3575761 + bNorm * 0.1804375;
        double y = rNorm * 0.2126729 + gNorm * 0.7151522 + bNorm * 0.0721750;
        double z = rNorm * 0.0193339 + gNorm * 0.1191920 + bNorm * 0.9503041;

        return new double[]{x * 100, y * 100, z * 100};
    }

    /**
     * XYZ转LAB色彩空间
     */
    private static double[] xyzToLab(double x, double y, double z) {
        // D65白点
        double xn = 95.047, yn = 100.000, zn = 108.883;

        double fx = f(x / xn);
        double fy = f(y / yn);
        double fz = f(z / zn);

        double l = 116 * fy - 16;
        double a = 500 * (fx - fy);
        double b = 200 * (fy - fz);

        return new double[]{l, a, b};
    }

    private static double f(double t) {
        return (t > 0.008856) ? Math.pow(t, 1.0/3.0) : (7.787 * t + 16.0/116.0);
    }

    /**
     * LAB转XYZ色彩空间
     */
    private static double[] labToXyz(double l, double a, double b) {
        double xn = 95.047, yn = 100.000, zn = 108.883;

        double fy = (l + 16) / 116;
        double fx = a / 500 + fy;
        double fz = fy - b / 200;

        double x = xn * fInv(fx);
        double y = yn * fInv(fy);
        double z = zn * fInv(fz);

        return new double[]{x, y, z};
    }

    private static double fInv(double t) {
        return (t > 0.206897) ? Math.pow(t, 3) : (t - 16.0/116.0) / 7.787;
    }

    /**
     * XYZ转RGB色彩空间
     */
    private static int[] xyzToRgb(double x, double y, double z) {
        x /= 100; y /= 100; z /= 100;

        double r = x * 3.2404542 + y * -1.5371385 + z * -0.4985314;
        double g = x * -0.9692660 + y * 1.8760108 + z * 0.0415560;
        double b = x * 0.0556434 + y * -0.2040259 + z * 1.0572252;

        // Gamma校正
        r = (r > 0.0031308) ? 1.055 * Math.pow(r, 1.0/2.4) - 0.055 : 12.92 * r;
        g = (g > 0.0031308) ? 1.055 * Math.pow(g, 1.0/2.4) - 0.055 : 12.92 * g;
        b = (b > 0.0031308) ? 1.055 * Math.pow(b, 1.0/2.4) - 0.055 : 12.92 * b;

        return new int[]{
            Math.max(0, Math.min(255, (int) Math.round(r * 255))),
            Math.max(0, Math.min(255, (int) Math.round(g * 255))),
            Math.max(0, Math.min(255, (int) Math.round(b * 255)))
        };
    }

    /**
     * 计算LAB色彩空间中的距离
     */
    private static double labDistance(double[] lab1, double[] lab2) {
        double dl = lab1[0] - lab2[0];
        double da = lab1[1] - lab2[1];
        double db = lab1[2] - lab2[2];
        double dalpha = lab1[3] - lab2[3];

        // 使用Delta E CIE76公式，并考虑alpha通道
        return Math.sqrt(dl * dl + da * da + db * db + dalpha * dalpha * 0.5);
    }

    /**
     * 创建基本调色板
     */
    private static IndexColorModel createBasicPalette() {
        byte[] r = new byte[256];
        byte[] g = new byte[256];
        byte[] b = new byte[256];
        byte[] a = new byte[256];

        // 透明色
        r[0] = 0; g[0] = 0; b[0] = 0; a[0] = 0;

        // 基本颜色
        for (int i = 1; i < 256; i++) {
            r[i] = (byte) ((i * 255) / 255);
            g[i] = (byte) ((i * 255) / 255);
            b[i] = (byte) ((i * 255) / 255);
            a[i] = (byte) 255;
        }

        return new IndexColorModel(8, 256, r, g, b, a);
    }

    // 辅助方法：Java 17以下没有Math.clamp，需手动实现
    private static int clamp(int value, int min, int max) {
        return Math.max(min, Math.min(max, value));
    }
}
