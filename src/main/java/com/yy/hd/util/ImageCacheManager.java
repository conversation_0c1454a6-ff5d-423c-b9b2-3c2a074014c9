package com.yy.hd.util;

import com.yy.hd.config.CacheConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HexFormat;

/**
 * 图片缓存管理器
 * 负责管理TinyPNG压缩后图片的磁盘缓存
 * <AUTHOR> 2025/6/5
 */
@Component
public class ImageCacheManager {

    private static final Logger logger = LoggerFactory.getLogger(ImageCacheManager.class);

    @Autowired
    private CacheConfig cacheConfig;
    
    /**
     * 根据输入流内容生成缓存键
     * @param inputData 输入图片数据
     * @return 缓存键（MD5哈希值）
     */
    public String generateCacheKey(byte[] inputData) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(inputData);
            return HexFormat.of().formatHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }
    
    /**
     * 获取缓存文件路径
     * @param cacheKey 缓存键
     * @return 缓存文件路径
     */
    public Path getCacheFilePath(String cacheKey) {
        return Paths.get(cacheConfig.getDirectory(), cacheKey + ".png");
    }
    
    /**
     * 检查缓存是否存在且未过期
     * @param cacheKey 缓存键
     * @return 是否存在有效缓存
     */
    public boolean isCacheValid(String cacheKey) {
        if (!cacheConfig.isEnabled()) {
            return false;
        }
        
        Path cacheFile = getCacheFilePath(cacheKey);
        if (!Files.exists(cacheFile)) {
            return false;
        }
        
        try {
            // 检查文件是否过期
            long fileTime = Files.getLastModifiedTime(cacheFile).toMillis();
            long expireTime = System.currentTimeMillis() - (cacheConfig.getExpireDays() * 24L * 60L * 60L * 1000L);
            
            return fileTime > expireTime;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * 从缓存读取压缩后的图片数据
     * @param cacheKey 缓存键
     * @return 压缩后的图片数据，如果缓存不存在返回null
     */
    public byte[] readFromCache(String cacheKey) {
        if (!isCacheValid(cacheKey)) {
            return null;
        }
        
        Path cacheFile = getCacheFilePath(cacheKey);
        try {
            return Files.readAllBytes(cacheFile);
        } catch (IOException e) {
            logger.error("读取缓存文件失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 将压缩后的图片数据写入缓存
     * @param cacheKey 缓存键
     * @param compressedData 压缩后的图片数据
     */
    public void writeToCache(String cacheKey, byte[] compressedData) {
        if (!cacheConfig.isEnabled()) {
            return;
        }
        
        try {
            Path cacheFile = getCacheFilePath(cacheKey);
            // 确保缓存目录存在
            Files.createDirectories(cacheFile.getParent());
            
            // 写入缓存文件
            Files.write(cacheFile, compressedData);

            logger.info("图片已缓存: {} (大小: {} 字节)", cacheKey, compressedData.length);
        } catch (IOException e) {
            logger.error("写入缓存文件失败: {}", e.getMessage());
        }
    }
    
    /**
     * 清理过期的缓存文件
     */
    public void cleanExpiredCache() {
        if (!cacheConfig.isEnabled()) {
            return;
        }
        
        try {
            Path cacheDir = Paths.get(cacheConfig.getDirectory());
            if (!Files.exists(cacheDir)) {
                return;
            }
            
            long expireTime = System.currentTimeMillis() - (cacheConfig.getExpireDays() * 24L * 60L * 60L * 1000L);
            
            Files.walk(cacheDir)
                .filter(Files::isRegularFile)
                .filter(path -> {
                    try {
                        return Files.getLastModifiedTime(path).toMillis() < expireTime;
                    } catch (IOException e) {
                        return false;
                    }
                })
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        logger.info("删除过期缓存文件: {}", path.getFileName());
                    } catch (IOException e) {
                        logger.error("删除过期缓存文件失败: {}", e.getMessage());
                    }
                });
        } catch (IOException e) {
            logger.error("清理过期缓存失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取缓存统计信息
     * @return 缓存统计信息字符串
     */
    public String getCacheStats() {
        if (!cacheConfig.isEnabled()) {
            return "缓存已禁用";
        }
        
        try {
            Path cacheDir = Paths.get(cacheConfig.getDirectory());
            if (!Files.exists(cacheDir)) {
                return "缓存目录不存在";
            }
            
            long fileCount = Files.walk(cacheDir)
                .filter(Files::isRegularFile)
                .count();
            
            long totalSize = Files.walk(cacheDir)
                .filter(Files::isRegularFile)
                .mapToLong(path -> {
                    try {
                        return Files.size(path);
                    } catch (IOException e) {
                        return 0;
                    }
                })
                .sum();
            
            return String.format("缓存文件数: %d, 总大小: %.2f MB", fileCount, totalSize / 1024.0 / 1024.0);
        } catch (IOException e) {
            return "获取缓存统计信息失败: " + e.getMessage();
        }
    }
}
