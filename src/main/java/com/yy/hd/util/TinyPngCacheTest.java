package com.yy.hd.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * TinyPNG缓存功能测试类
 * <AUTHOR> 2025/6/5
 */
@Component
public class TinyPngCacheTest implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(TinyPngCacheTest.class);

    @Autowired
    private TinyPngUtil tinyPngUtil;

    @Autowired
    private ImageCacheManager cacheManager;
    
    @Override
    public void run(String... args) throws Exception {
        // 只在有测试图片时运行测试
        String testImagePath = "src/main/java/com/yy/hd/util/before.png";
        if (!Files.exists(Paths.get(testImagePath))) {
            logger.info("测试图片不存在，跳过缓存测试: {}", testImagePath);
            return;
        }

//        logger.info("=== TinyPNG缓存功能测试 ===");
//        testCacheFunction();
    }
    
    private void testCacheFunction() {
        try {
            String testImagePath = "src/main/java/com/yy/hd/util/before.png";
            String outputPath1 = "src/main/java/com/yy/hd/util/cache_test_1.png";
            String outputPath2 = "src/main/java/com/yy/hd/util/cache_test_2.png";
            
            // 第一次压缩（应该调用API）
            logger.info("--- 第一次压缩测试 ---");
            long startTime1 = System.currentTimeMillis();

            try (FileInputStream input1 = new FileInputStream(testImagePath);
                 FileOutputStream output1 = new FileOutputStream(outputPath1)) {
                tinyPngUtil.compress(input1, output1);
            }

            long duration1 = System.currentTimeMillis() - startTime1;
            logger.info("第一次压缩耗时: {}ms", duration1);
            
            // 等待一秒确保缓存写入完成
            Thread.sleep(1000);
            
            // 第二次压缩相同图片（应该使用缓存）
            logger.info("--- 第二次压缩测试（相同图片） ---");
            long startTime2 = System.currentTimeMillis();

            try (FileInputStream input2 = new FileInputStream(testImagePath);
                 FileOutputStream output2 = new FileOutputStream(outputPath2)) {
                tinyPngUtil.compress(input2, output2);
            }

            long duration2 = System.currentTimeMillis() - startTime2;
            logger.info("第二次压缩耗时: {}ms", duration2);
            
            // 比较两次压缩的结果
            byte[] result1 = Files.readAllBytes(Paths.get(outputPath1));
            byte[] result2 = Files.readAllBytes(Paths.get(outputPath2));
            
            boolean identical = java.util.Arrays.equals(result1, result2);
            logger.info("两次压缩结果是否相同: {}", identical);
            logger.info("压缩后文件大小: {} 字节", result1.length);

            // 显示缓存统计
            logger.info("--- 缓存统计信息 ---");
            logger.info(cacheManager.getCacheStats());

            // 性能对比
            if (duration2 < duration1) {
                double speedup = (double) duration1 / duration2;
                logger.info("缓存加速比: {}x", String.format("%.2f", speedup));
            }
            
            // 清理测试文件
            Files.deleteIfExists(Paths.get(outputPath1));
            Files.deleteIfExists(Paths.get(outputPath2));

            logger.info("=== 缓存功能测试完成 ===");

        } catch (Exception e) {
            logger.error("缓存测试失败: {}", e.getMessage(), e);
        }
    }
}
