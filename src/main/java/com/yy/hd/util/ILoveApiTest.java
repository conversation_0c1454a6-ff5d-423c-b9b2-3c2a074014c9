package com.yy.hd.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * iLoveAPI功能测试类
 * <AUTHOR> 2025/6/5
 */
@Component
public class ILoveApiTest implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ILoveApiTest.class);

    @Autowired
    private ILoveApiUtil iLoveApiUtil;

    @Override
    public void run(String... args) throws Exception {
        // 只在有测试图片时运行测试
        String testImagePath = "src/main/java/com/yy/hd/util/before.png";
        if (!Files.exists(Paths.get(testImagePath))) {
            logger.info("测试图片不存在，跳过iLoveAPI测试: {}", testImagePath);
            return;
        }

        // 注释掉测试，避免在每次启动时都调用API
        // logger.info("=== iLoveAPI功能测试 ===");
//         testILoveApiCompression();
    }

    private void testILoveApiCompression() {
        try {
            String testImagePath = "src/main/java/com/yy/hd/util/before.png";
            String outputPath = "src/main/java/com/yy/hd/util/iloveapi_test.png";

            logger.info("--- iLoveAPI压缩测试 ---");
            long startTime = System.currentTimeMillis();

            try (FileInputStream input = new FileInputStream(testImagePath);
                 FileOutputStream output = new FileOutputStream(outputPath)) {
                iLoveApiUtil.compress(input, output);
            }

            long duration = System.currentTimeMillis() - startTime;
            logger.info("iLoveAPI压缩耗时: {}ms", duration);

            // 比较文件大小
            long originalSize = Files.size(Paths.get(testImagePath));
            long compressedSize = Files.size(Paths.get(outputPath));

            logger.info("原始文件大小: {} 字节", originalSize);
            logger.info("压缩后文件大小: {} 字节", compressedSize);

            if (compressedSize < originalSize) {
                double compressionRatio = (double) (originalSize - compressedSize) / originalSize * 100;
                logger.info("压缩率: {:.2f}%", compressionRatio);
            } else {
                logger.info("压缩后文件更大，可能使用了原图");
            }

            // 清理测试文件
            Files.deleteIfExists(Paths.get(outputPath));

            logger.info("=== iLoveAPI测试完成 ===");

        } catch (Exception e) {
            logger.error("iLoveAPI测试失败: {}", e.getMessage(), e);
        }
    }
}
