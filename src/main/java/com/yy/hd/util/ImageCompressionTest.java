package com.yy.hd.util;

import java.io.*;

/**
 * 图片压缩测试类
 * 用于测试和分析压缩前后的图片质量问题
 */
public class ImageCompressionTest {
    
    public static void main(String[] args) {
        try {
            // 测试压缩前后的图片
            String beforePath = "src/main/java/com/yy/hd/util/before.png";
            String afterPath = "src/main/java/com/yy/hd/util/after_test.png";
            
            System.out.println("开始测试图片压缩...");
            
            // 读取原图
            FileInputStream input = new FileInputStream(beforePath);
            FileOutputStream output = new FileOutputStream(afterPath);
            
            // 使用ImageUtil进行压缩
            ImageUtil.compress(input, output);
            
            input.close();
            output.close();
            
            System.out.println("压缩完成，结果保存到: " + afterPath);
            
            // 比较文件大小
            File beforeFile = new File(beforePath);
            File afterFile = new File(afterPath);
            
            System.out.println("原图大小: " + beforeFile.length() + " 字节");
            System.out.println("压缩后大小: " + afterFile.length() + " 字节");
            System.out.println("压缩率: " + String.format("%.2f%%", 
                (1.0 - (double)afterFile.length() / beforeFile.length()) * 100));
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
