package com.yy.hd.util;

import com.tinify.Tinify;
import com.yy.hd.config.TinyPngKeyManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * TinyPNG压缩工具类，支持磁盘缓存
 * <AUTHOR> 2025/6/5
 */
@Component
public class TinyPngUtil {

    private static final Logger logger = LoggerFactory.getLogger(TinyPngUtil.class);

    @Autowired
    private ImageCacheManager cacheManager;

    @Autowired
    private TinyPngKeyManager keyManager;

    /**
     * 压缩图片，支持缓存
     * @param input 输入流
     * @param output 输出流
     * @throws IOException IO异常
     */
    public void compress(InputStream input, OutputStream output) throws IOException {
        // 读取输入数据
        byte[] inputData = input.readAllBytes();

        // 生成缓存键
        String cacheKey = cacheManager.generateCacheKey(inputData);

        // 检查缓存
        byte[] cachedData = cacheManager.readFromCache(cacheKey);
        if (cachedData != null) {
            logger.info("使用缓存的压缩图片: {}", cacheKey);
            output.write(cachedData);
            return;
        }

        // 缓存不存在，调用TinyPNG API进行压缩
        logger.info("调用TinyPNG API压缩图片: {}", cacheKey);

        // 获取可用的API key
        String apiKey = keyManager.getNextAvailableKey();
        if (apiKey == null) {
            logger.error("没有可用的TinyPNG API key，使用原图");
            output.write(inputData);
            return;
        }

        // 设置当前API key
        Tinify.setKey(apiKey);

        byte[] compressedData = Tinify.fromBuffer(inputData).toBuffer();

        // 检查压缩效果，只有压缩后更小才缓存和使用
        if (compressedData.length < inputData.length) {
            // 写入缓存
            cacheManager.writeToCache(cacheKey, compressedData);

            // 输出压缩后的数据
            output.write(compressedData);

            logger.info("压缩成功: {} -> {} 字节", inputData.length, compressedData.length);
        } else {
            // 压缩后反而更大，使用原图
            logger.warn("压缩后文件更大，使用原图: {} vs {} 字节", inputData.length, compressedData.length);
            output.write(inputData);
        }
    }

    /**
     * 静态方法保持向后兼容，但不支持缓存
     * @param input 输入流
     * @param output 输出流
     * @throws IOException IO异常
     * @deprecated 建议使用注入的实例方法以支持缓存功能
     */
    @Deprecated
    public static void compressStatic(InputStream input, OutputStream output) throws IOException {
        byte[] resultData = Tinify.fromBuffer(input.readAllBytes()).toBuffer();
        output.write(resultData);
    }
}
