package com.yy.hd.util;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.IndexColorModel;
import java.io.*;
import java.util.*;

/**
 * 色彩分析测试类
 * 对比我们的压缩结果与TinyPNG的结果
 */
public class ColorAnalysisTest {
    
    public static void main(String[] args) {
        try {
            String originalPath = "src/main/java/com/yy/hd/util/before.png";
            String ourResultPath = "src/main/java/com/yy/hd/util/after_test.png";
            String tinypngPath = "src/main/java/com/yy/hd/util/tinypng.png";
            
            System.out.println("=== 色彩质量对比分析 ===");
            
            BufferedImage original = ImageIO.read(new File(originalPath));
            BufferedImage ourResult = ImageIO.read(new File(ourResultPath));
            BufferedImage tinypngResult = ImageIO.read(new File(tinypngPath));
            
            System.out.println("原图: " + original.getWidth() + "x" + original.getHeight() + 
                " (" + getImageTypeString(original.getType()) + ")");
            System.out.println("我们的结果: " + ourResult.getWidth() + "x" + ourResult.getHeight() + 
                " (" + getImageTypeString(ourResult.getType()) + ")");
            System.out.println("TinyPNG结果: " + tinypngResult.getWidth() + "x" + tinypngResult.getHeight() + 
                " (" + getImageTypeString(tinypngResult.getType()) + ")");
            
            // 文件大小对比
            System.out.println("\n=== 文件大小对比 ===");
            File originalFile = new File(originalPath);
            File ourFile = new File(ourResultPath);
            File tinypngFile = new File(tinypngPath);
            
            System.out.println("原图: " + formatFileSize(originalFile.length()));
            System.out.println("我们的结果: " + formatFileSize(ourFile.length()) + 
                " (压缩率: " + String.format("%.2f%%", 
                (1.0 - (double)ourFile.length() / originalFile.length()) * 100) + ")");
            System.out.println("TinyPNG: " + formatFileSize(tinypngFile.length()) + 
                " (压缩率: " + String.format("%.2f%%", 
                (1.0 - (double)tinypngFile.length() / originalFile.length()) * 100) + ")");
            
            // 色彩分析
            System.out.println("\n=== 色彩分析 ===");
            analyzeColors(original, "原图");
            analyzeColors(ourResult, "我们的结果");
            analyzeColors(tinypngResult, "TinyPNG结果");
            
            // PSNR对比
            System.out.println("\n=== 质量对比 (PSNR) ===");
            double ourPSNR = calculatePSNR(original, ourResult);
            double tinypngPSNR = calculatePSNR(original, tinypngResult);
            
            System.out.println("我们的结果 PSNR: " + String.format("%.2f dB", ourPSNR));
            System.out.println("TinyPNG PSNR: " + String.format("%.2f dB", tinypngPSNR));
            
            if (tinypngPSNR > ourPSNR) {
                System.out.println("TinyPNG质量更好，差距: " + String.format("%.2f dB", tinypngPSNR - ourPSNR));
            } else {
                System.out.println("我们的结果质量更好，差距: " + String.format("%.2f dB", ourPSNR - tinypngPSNR));
            }
            
            // 色彩保真度分析
            System.out.println("\n=== 色彩保真度分析 ===");
            analyzeColorFidelity(original, ourResult, tinypngResult);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void analyzeColors(BufferedImage image, String name) {
        System.out.println("\n" + name + ":");
        System.out.println("  图片类型: " + getImageTypeString(image.getType()));
        System.out.println("  位深度: " + image.getColorModel().getPixelSize());
        
        if (image.getType() == BufferedImage.TYPE_BYTE_INDEXED) {
            IndexColorModel icm = (IndexColorModel) image.getColorModel();
            System.out.println("  调色板大小: " + icm.getMapSize());
            
            // 分析调色板中的颜色分布
            Set<Integer> usedColors = new HashSet<>();
            for (int y = 0; y < image.getHeight(); y++) {
                for (int x = 0; x < image.getWidth(); x++) {
                    usedColors.add(image.getRaster().getSample(x, y, 0));
                }
            }
            System.out.println("  实际使用颜色: " + usedColors.size());
            
            // 分析透明度
            boolean hasTransparency = false;
            for (int i = 0; i < icm.getMapSize(); i++) {
                if (icm.getAlpha(i) < 255) {
                    hasTransparency = true;
                    break;
                }
            }
            System.out.println("  支持透明度: " + hasTransparency);
        } else {
            // 统计实际颜色数量
            Set<Integer> uniqueColors = new HashSet<>();
            for (int y = 0; y < image.getHeight(); y++) {
                for (int x = 0; x < image.getWidth(); x++) {
                    uniqueColors.add(image.getRGB(x, y));
                }
            }
            System.out.println("  唯一颜色数: " + uniqueColors.size());
        }
    }
    
    private static double calculatePSNR(BufferedImage original, BufferedImage compressed) {
        int width = Math.min(original.getWidth(), compressed.getWidth());
        int height = Math.min(original.getHeight(), compressed.getHeight());
        
        double mse = 0;
        int totalPixels = width * height;
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int originalRgb = original.getRGB(x, y);
                int compressedRgb = compressed.getRGB(x, y);
                
                int origR = (originalRgb >> 16) & 0xFF;
                int origG = (originalRgb >> 8) & 0xFF;
                int origB = originalRgb & 0xFF;
                
                int compR = (compressedRgb >> 16) & 0xFF;
                int compG = (compressedRgb >> 8) & 0xFF;
                int compB = compressedRgb & 0xFF;
                
                mse += Math.pow(origR - compR, 2) + Math.pow(origG - compG, 2) + Math.pow(origB - compB, 2);
            }
        }
        
        mse /= (totalPixels * 3);
        
        if (mse == 0) return Double.POSITIVE_INFINITY;
        return 20 * Math.log10(255.0 / Math.sqrt(mse));
    }
    
    private static void analyzeColorFidelity(BufferedImage original, BufferedImage ours, BufferedImage tinypng) {
        int width = original.getWidth();
        int height = original.getHeight();
        
        // 分析色彩偏差分布
        double[] ourErrors = new double[width * height];
        double[] tinypngErrors = new double[width * height];
        
        int index = 0;
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int origRgb = original.getRGB(x, y);
                int ourRgb = ours.getRGB(x, y);
                int tinypngRgb = tinypng.getRGB(x, y);
                
                ourErrors[index] = calculateColorDistance(origRgb, ourRgb);
                tinypngErrors[index] = calculateColorDistance(origRgb, tinypngRgb);
                index++;
            }
        }
        
        Arrays.sort(ourErrors);
        Arrays.sort(tinypngErrors);
        
        System.out.println("色彩误差统计:");
        System.out.println("  我们的结果 - 平均误差: " + String.format("%.2f", average(ourErrors)));
        System.out.println("  我们的结果 - 中位数误差: " + String.format("%.2f", ourErrors[ourErrors.length/2]));
        System.out.println("  我们的结果 - 95%分位数: " + String.format("%.2f", ourErrors[(int)(ourErrors.length * 0.95)]));
        
        System.out.println("  TinyPNG - 平均误差: " + String.format("%.2f", average(tinypngErrors)));
        System.out.println("  TinyPNG - 中位数误差: " + String.format("%.2f", tinypngErrors[tinypngErrors.length/2]));
        System.out.println("  TinyPNG - 95%分位数: " + String.format("%.2f", tinypngErrors[(int)(tinypngErrors.length * 0.95)]));
    }
    
    private static double calculateColorDistance(int rgb1, int rgb2) {
        int r1 = (rgb1 >> 16) & 0xFF;
        int g1 = (rgb1 >> 8) & 0xFF;
        int b1 = rgb1 & 0xFF;
        
        int r2 = (rgb2 >> 16) & 0xFF;
        int g2 = (rgb2 >> 8) & 0xFF;
        int b2 = rgb2 & 0xFF;
        
        return Math.sqrt(Math.pow(r1 - r2, 2) + Math.pow(g1 - g2, 2) + Math.pow(b1 - b2, 2));
    }
    
    private static double average(double[] array) {
        double sum = 0;
        for (double value : array) {
            sum += value;
        }
        return sum / array.length;
    }
    
    private static String getImageTypeString(int type) {
        switch (type) {
            case BufferedImage.TYPE_INT_RGB: return "INT_RGB";
            case BufferedImage.TYPE_INT_ARGB: return "INT_ARGB";
            case BufferedImage.TYPE_INT_ARGB_PRE: return "INT_ARGB_PRE";
            case BufferedImage.TYPE_INT_BGR: return "INT_BGR";
            case BufferedImage.TYPE_3BYTE_BGR: return "3BYTE_BGR";
            case BufferedImage.TYPE_4BYTE_ABGR: return "4BYTE_ABGR";
            case BufferedImage.TYPE_4BYTE_ABGR_PRE: return "4BYTE_ABGR_PRE";
            case BufferedImage.TYPE_BYTE_GRAY: return "BYTE_GRAY";
            case BufferedImage.TYPE_USHORT_GRAY: return "USHORT_GRAY";
            case BufferedImage.TYPE_BYTE_BINARY: return "BYTE_BINARY";
            case BufferedImage.TYPE_BYTE_INDEXED: return "BYTE_INDEXED";
            case BufferedImage.TYPE_USHORT_565_RGB: return "USHORT_565_RGB";
            case BufferedImage.TYPE_USHORT_555_RGB: return "USHORT_555_RGB";
            default: return "CUSTOM(" + type + ")";
        }
    }
    
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
    }
}
