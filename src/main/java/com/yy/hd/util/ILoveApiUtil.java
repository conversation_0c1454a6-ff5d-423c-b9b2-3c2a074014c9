package com.yy.hd.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yy.hd.config.ILoveApiKeyManager;
import com.yy.hd.config.ILoveApiProperties;
import com.yy.hd.dto.ILoveApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * iLoveAPI图片压缩工具类
 * 实现异步图片压缩：/auth → /start → /upload → /process → /download
 * <AUTHOR> 2025/6/5
 */
@Component
public class ILoveApiUtil {

    private static final Logger logger = LoggerFactory.getLogger(ILoveApiUtil.class);

    @Autowired
    private ILoveApiProperties properties;

    @Autowired
    private ImageCacheManager cacheManager;

    @Autowired
    private ILoveApiKeyManager keyManager;

    private final ObjectMapper objectMapper;

    public ILoveApiUtil() {
        this.objectMapper = new ObjectMapper();
        // 配置忽略未识别的字段
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 压缩图片，支持缓存
     * @param input 输入流
     * @param output 输出流
     * @throws IOException IO异常
     */
    public void compress(InputStream input, OutputStream output) throws IOException {
        if (!properties.isEnabled()) {
            logger.warn("iLoveAPI未启用，使用原图");
            input.transferTo(output);
            return;
        }

        // 读取输入数据
        byte[] inputData = input.readAllBytes();

        // 生成缓存键
        String cacheKey = "iloveapi_" + cacheManager.generateCacheKey(inputData);

        // 检查缓存
        byte[] cachedData = cacheManager.readFromCache(cacheKey);
        if (cachedData != null) {
            logger.info("使用缓存的iLoveAPI压缩图片: {}", cacheKey);
            output.write(cachedData);
            return;
        }

        try {
            // 执行异步压缩流程
            logger.info("开始iLoveAPI异步压缩流程: {}", cacheKey);
            byte[] compressedData = performAsyncCompression(inputData);

            // 检查压缩效果，只有压缩后更小才缓存和使用
            if (compressedData.length < inputData.length) {
                // 写入缓存
                cacheManager.writeToCache(cacheKey, compressedData);

                // 输出压缩后的数据
                output.write(compressedData);

                logger.info("iLoveAPI压缩成功: {} -> {} 字节", inputData.length, compressedData.length);
            } else {
                // 压缩后反而更大，使用原图
                logger.warn("iLoveAPI压缩后文件更大，使用原图: {} vs {} 字节", inputData.length, compressedData.length);
                output.write(inputData);
            }
        } catch (Exception e) {
            logger.error("iLoveAPI压缩失败，使用原图: {}", e.getMessage());
            output.write(inputData);
        }
    }

    /**
     * 执行异步压缩流程
     * @param inputData 输入数据
     * @return 压缩后的数据
     * @throws IOException IO异常
     */
    private byte[] performAsyncCompression(byte[] inputData) throws IOException {
        // 1. 获取认证token
        String token = authenticate();
        logger.debug("获取认证token成功");

        // 2. 开始任务
        ILoveApiResponse.StartResponse startResponse = startTask(token, "compressimage");
        logger.debug("开始任务成功: server={}, task={}", startResponse.getServer(), startResponse.getTask());

        // 3. 上传文件
        ILoveApiResponse.UploadResponse uploadResponse = uploadFile(
            startResponse.getServer(), 
            token, 
            startResponse.getTask(), 
            inputData, 
            "image.jpg"
        );
        logger.debug("上传文件成功: {}", uploadResponse.getServerFilename());

        // 4. 处理任务
        ILoveApiResponse.ProcessResponse processResponse = processTask(
            startResponse.getServer(),
            token,
            startResponse.getTask(),
            uploadResponse.getServerFilename(),
            "image.jpg"
        );
        logger.debug("处理任务成功: status={}", processResponse.getStatus());

        // 5. 下载结果
        byte[] result = downloadResult(startResponse.getServer(), token, startResponse.getTask());
        logger.debug("下载结果成功: {} 字节", result.length);

        return result;
    }

    /**
     * 1. 认证获取token
     */
    private String authenticate() throws IOException {
        String url = properties.getBaseUrl() + "/auth";

        // 获取可用的公钥
        String publicKey = keyManager.getNextAvailableKey();
        if (publicKey == null) {
            throw new IOException("没有可用的iLoveAPI公钥");
        }

        Map<String, String> params = new HashMap<>();
        params.put("public_key", publicKey);

        String response = sendPostRequest(url, params, null);
        ILoveApiResponse.AuthResponse authResponse = objectMapper.readValue(response, ILoveApiResponse.AuthResponse.class);

        return authResponse.getToken();
    }

    /**
     * 2. 开始任务
     */
    private ILoveApiResponse.StartResponse startTask(String token, String tool) throws IOException {
        String url = properties.getBaseUrl() + "/start/" + tool;
        
        String response = sendGetRequest(url, token);
        return objectMapper.readValue(response, ILoveApiResponse.StartResponse.class);
    }

    /**
     * 3. 上传文件
     */
    private ILoveApiResponse.UploadResponse uploadFile(String server, String token, String task, 
                                                      byte[] fileData, String filename) throws IOException {
        String url = "https://" + server + "/v1/upload";
        
        Map<String, String> params = new HashMap<>();
        params.put("task", task);

        String response = sendMultipartRequest(url, params, fileData, filename, token);
        return objectMapper.readValue(response, ILoveApiResponse.UploadResponse.class);
    }

    /**
     * 4. 处理任务
     */
    private ILoveApiResponse.ProcessResponse processTask(String server, String token, String task, 
                                                        String serverFilename, String originalFilename) throws IOException {
        String url = "https://" + server + "/v1/process";
        
        Map<String, Object> params = new HashMap<>();
        params.put("task", task);
        params.put("tool", "compressimage");
        
        // 构建文件信息
        ILoveApiResponse.FileInfo fileInfo = new ILoveApiResponse.FileInfo(serverFilename, originalFilename);
        params.put("files", Collections.singletonList(fileInfo));
        
        // 压缩参数
        params.put("compression_level", "recommended"); // extreme, recommended, low

        String jsonParams = objectMapper.writeValueAsString(params);
        String response = sendJsonRequest(url, jsonParams, token);
        
        return objectMapper.readValue(response, ILoveApiResponse.ProcessResponse.class);
    }

    /**
     * 5. 下载结果
     */
    private byte[] downloadResult(String server, String token, String task) throws IOException {
        String url = "https://" + server + "/v1/download/" + task;
        
        return sendDownloadRequest(url, token);
    }

    /**
     * 发送GET请求
     */
    private String sendGetRequest(String urlString, String token) throws IOException {
        logger.debug("发送GET请求: URL={}, Token={}", urlString, token != null ? "Bearer " + token.substring(0, Math.min(20, token.length())) + "..." : "null");

        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();

        conn.setRequestMethod("GET");
        conn.setConnectTimeout(properties.getConnectTimeout() * 1000);
        conn.setReadTimeout(properties.getReadTimeout() * 1000);
        conn.setRequestProperty("Authorization", "Bearer " + token);
        conn.setRequestProperty("Content-Type", "application/json");

        String response = readResponse(conn);
        logger.debug("GET请求响应: {}", response);

        return response;
    }

    /**
     * 发送POST请求（表单数据）
     */
    private String sendPostRequest(String urlString, Map<String, String> params, String token) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setRequestMethod("POST");
        conn.setConnectTimeout(properties.getConnectTimeout() * 1000);
        conn.setReadTimeout(properties.getReadTimeout() * 1000);
        conn.setDoOutput(true);
        
        if (token != null) {
            conn.setRequestProperty("Authorization", "Bearer " + token);
        }
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

        // 构建表单数据
        StringBuilder postData = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (postData.length() > 0) {
                postData.append("&");
            }
            postData.append(entry.getKey()).append("=").append(entry.getValue());
        }

        try (OutputStream os = conn.getOutputStream()) {
            os.write(postData.toString().getBytes(StandardCharsets.UTF_8));
        }

        return readResponse(conn);
    }

    /**
     * 发送JSON请求
     */
    private String sendJsonRequest(String urlString, String jsonData, String token) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        conn.setRequestMethod("POST");
        conn.setConnectTimeout(properties.getConnectTimeout() * 1000);
        conn.setReadTimeout(properties.getReadTimeout() * 1000);
        conn.setDoOutput(true);
        conn.setRequestProperty("Authorization", "Bearer " + token);
        conn.setRequestProperty("Content-Type", "application/json");

        try (OutputStream os = conn.getOutputStream()) {
            os.write(jsonData.getBytes(StandardCharsets.UTF_8));
        }

        return readResponse(conn);
    }

    /**
     * 发送multipart请求（文件上传）
     */
    private String sendMultipartRequest(String urlString, Map<String, String> params,
                                       byte[] fileData, String filename, String token) throws IOException {
        String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();

        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();

        conn.setRequestMethod("POST");
        conn.setConnectTimeout(properties.getConnectTimeout() * 1000);
        conn.setReadTimeout(properties.getReadTimeout() * 1000);
        conn.setDoOutput(true);
        conn.setRequestProperty("Authorization", "Bearer " + token);
        conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

        try (OutputStream os = conn.getOutputStream()) {
            // 写入表单参数
            for (Map.Entry<String, String> entry : params.entrySet()) {
                os.write(("--" + boundary + "\r\n").getBytes());
                os.write(("Content-Disposition: form-data; name=\"" + entry.getKey() + "\"\r\n\r\n").getBytes());
                os.write((entry.getValue() + "\r\n").getBytes());
            }

            // 写入文件数据
            os.write(("--" + boundary + "\r\n").getBytes());
            os.write(("Content-Disposition: form-data; name=\"file\"; filename=\"" + filename + "\"\r\n").getBytes());
            os.write("Content-Type: application/octet-stream\r\n\r\n".getBytes());
            os.write(fileData);
            os.write(("\r\n--" + boundary + "--\r\n").getBytes());
        }

        return readResponse(conn);
    }

    /**
     * 发送下载请求
     */
    private byte[] sendDownloadRequest(String urlString, String token) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();

        conn.setRequestMethod("GET");
        conn.setConnectTimeout(properties.getConnectTimeout() * 1000);
        conn.setReadTimeout(properties.getReadTimeout() * 1000);
        conn.setRequestProperty("Authorization", "Bearer " + token);

        int responseCode = conn.getResponseCode();
        if (responseCode != HttpURLConnection.HTTP_OK) {
            String errorMessage = readErrorResponse(conn);
            throw new IOException("下载失败: HTTP " + responseCode + " - " + errorMessage);
        }

        try (InputStream is = conn.getInputStream()) {
            return is.readAllBytes();
        }
    }

    /**
     * 读取响应内容
     */
    private String readResponse(HttpURLConnection conn) throws IOException {
        int responseCode = conn.getResponseCode();

        if (responseCode >= 200 && responseCode < 300) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            }
        } else {
            String errorMessage = readErrorResponse(conn);
            throw new IOException("请求失败: HTTP " + responseCode + " - " + errorMessage);
        }
    }

    /**
     * 读取错误响应
     */
    private String readErrorResponse(HttpURLConnection conn) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getErrorStream()))) {
            StringBuilder error = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                error.append(line);
            }
            return error.toString();
        } catch (Exception e) {
            return "无法读取错误信息";
        }
    }

    /**
     * 静态方法保持向后兼容，但不支持缓存
     * @param input 输入流
     * @param output 输出流
     * @throws IOException IO异常
     * @deprecated 建议使用注入的实例方法以支持缓存功能
     */
    @Deprecated
    public static void compressStatic(InputStream input, OutputStream output) throws IOException {
        // 静态方法无法使用Spring注入，直接传输原图
        input.transferTo(output);
    }
}
