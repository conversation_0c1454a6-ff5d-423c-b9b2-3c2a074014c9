package com.yy.hd.mapper;

import com.yy.hd.entity.RequestLog;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 请求日志Mapper
 * <AUTHOR> 2025/6/13
 */
@Mapper
public interface RequestLogMapper {
    
    /**
     * 插入请求日志
     */
    @Insert("INSERT INTO request_log (request_id, user_email, user_passport, user_yy, request_uri, request_method, " +
            "user_agent, client_ip, image_original_size, image_compressed_size, compression_method, compression_ratio, " +
            "processing_time_ms, success, error_message, created_time) " +
            "VALUES (#{requestId}, #{userEmail}, #{userPassport}, #{userYy}, #{requestUri}, #{requestMethod}, " +
            "#{userAgent}, #{clientIp}, #{imageOriginalSize}, #{imageCompressedSize}, #{compressionMethod}, #{compressionRatio}, " +
            "#{processingTimeMs}, #{success}, #{errorMessage}, #{createdTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(RequestLog requestLog);
    
    /**
     * 根据请求ID查询
     */
    @Select("SELECT * FROM request_log WHERE request_id = #{requestId}")
    RequestLog findByRequestId(@Param("requestId") String requestId);
    
    /**
     * 根据用户邮箱查询最近的请求日志
     */
    @Select("SELECT * FROM request_log WHERE user_email = #{userEmail} ORDER BY created_time DESC LIMIT #{limit}")
    List<RequestLog> findRecentByUserEmail(@Param("userEmail") String userEmail, @Param("limit") int limit);
    
    /**
     * 根据时间范围查询请求日志
     */
    @Select("SELECT * FROM request_log WHERE created_time >= #{startTime} AND created_time <= #{endTime} ORDER BY created_time DESC")
    List<RequestLog> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的请求数量
     */
    @Select("SELECT COUNT(*) FROM request_log WHERE created_time >= #{startTime} AND created_time <= #{endTime}")
    Long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定压缩方法的使用次数
     */
    @Select("SELECT COUNT(*) FROM request_log WHERE compression_method = #{compressionMethod} AND created_time >= #{startTime} AND created_time <= #{endTime}")
    Long countByCompressionMethod(@Param("compressionMethod") String compressionMethod, 
                                 @Param("startTime") LocalDateTime startTime, 
                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计成功和失败的请求数量
     */
    @Select("SELECT success, COUNT(*) as count FROM request_log WHERE created_time >= #{startTime} AND created_time <= #{endTime} GROUP BY success")
    @Results({
        @Result(property = "success", column = "success"),
        @Result(property = "count", column = "count")
    })
    List<Object> countBySuccessStatus(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
