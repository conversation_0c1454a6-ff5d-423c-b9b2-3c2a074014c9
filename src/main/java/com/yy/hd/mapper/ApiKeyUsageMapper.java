package com.yy.hd.mapper;

import com.yy.hd.entity.ApiKeyUsage;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API Key使用记录Mapper
 * <AUTHOR> 2025/6/13
 */
@Mapper
public interface ApiKeyUsageMapper {
    
    /**
     * 根据API Key哈希、服务类型和使用月份查询记录
     */
    @Select("SELECT * FROM api_key_usage WHERE api_key_hash = #{apiKeyHash} AND service_type = #{serviceType} AND usage_month = #{usageMonth}")
    ApiKeyUsage findByKeyAndServiceAndMonth(@Param("apiKeyHash") String apiKeyHash, 
                                           @Param("serviceType") String serviceType, 
                                           @Param("usageMonth") String usageMonth);
    
    /**
     * 插入新记录
     */
    @Insert("INSERT INTO api_key_usage (api_key_hash, service_type, usage_month, usage_count, last_used_time, created_time, updated_time) " +
            "VALUES (#{apiKeyHash}, #{serviceType}, #{usageMonth}, #{usageCount}, #{lastUsedTime}, #{createdTime}, #{updatedTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ApiKeyUsage apiKeyUsage);
    
    /**
     * 更新使用次数和最后使用时间
     */
    @Update("UPDATE api_key_usage SET usage_count = usage_count + 1, last_used_time = #{lastUsedTime}, updated_time = #{updatedTime} " +
            "WHERE api_key_hash = #{apiKeyHash} AND service_type = #{serviceType} AND usage_month = #{usageMonth}")
    int incrementUsage(@Param("apiKeyHash") String apiKeyHash, 
                      @Param("serviceType") String serviceType, 
                      @Param("usageMonth") String usageMonth,
                      @Param("lastUsedTime") LocalDateTime lastUsedTime,
                      @Param("updatedTime") LocalDateTime updatedTime);
    
    /**
     * 根据服务类型和使用月份查询所有记录
     */
    @Select("SELECT * FROM api_key_usage WHERE service_type = #{serviceType} AND usage_month = #{usageMonth}")
    List<ApiKeyUsage> findByServiceAndMonth(@Param("serviceType") String serviceType, 
                                           @Param("usageMonth") String usageMonth);
    
    /**
     * 重置指定月份的使用统计
     */
    @Delete("DELETE FROM api_key_usage WHERE service_type = #{serviceType} AND usage_month = #{usageMonth}")
    int deleteByServiceAndMonth(@Param("serviceType") String serviceType, 
                               @Param("usageMonth") String usageMonth);
    
    /**
     * 获取指定API Key的使用次数
     */
    @Select("SELECT COALESCE(usage_count, 0) FROM api_key_usage WHERE api_key_hash = #{apiKeyHash} AND service_type = #{serviceType} AND usage_month = #{usageMonth}")
    Integer getUsageCount(@Param("apiKeyHash") String apiKeyHash, 
                         @Param("serviceType") String serviceType, 
                         @Param("usageMonth") String usageMonth);
}
