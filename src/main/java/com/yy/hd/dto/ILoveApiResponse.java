package com.yy.hd.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * iLoveAPI响应数据类
 * <AUTHOR> 2025/6/5
 */
public class ILoveApiResponse {

    /**
     * 认证响应
     */
    public static class AuthResponse {
        private String token;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }
    }

    /**
     * 开始任务响应
     */
    public static class StartResponse {
        private String server;
        private String task;
        
        @JsonProperty("remaining_credits")
        private Integer remainingCredits;

        @JsonProperty("remaining_files")
        private Integer remainingFiles;

        public String getServer() {
            return server;
        }

        public void setServer(String server) {
            this.server = server;
        }

        public String getTask() {
            return task;
        }

        public void setTask(String task) {
            this.task = task;
        }

        public Integer getRemainingCredits() {
            return remainingCredits;
        }

        public void setRemainingCredits(Integer remainingCredits) {
            this.remainingCredits = remainingCredits;
        }

        public Integer getRemainingFiles() {
            return remainingFiles;
        }

        public void setRemainingFiles(Integer remainingFiles) {
            this.remainingFiles = remainingFiles;
        }
    }

    /**
     * 上传文件响应
     */
    public static class UploadResponse {
        @JsonProperty("server_filename")
        private String serverFilename;

        public String getServerFilename() {
            return serverFilename;
        }

        public void setServerFilename(String serverFilename) {
            this.serverFilename = serverFilename;
        }
    }

    /**
     * 处理任务响应
     */
    public static class ProcessResponse {
        private String status;
        
        @JsonProperty("status_message")
        private String statusMessage;
        
        private String timer;
        private Long filesize;
        
        @JsonProperty("output_filesize")
        private Long outputFilesize;
        
        @JsonProperty("output_filenumber")
        private Integer outputFilenumber;
        
        @JsonProperty("download_filename")
        private String downloadFilename;

        @JsonProperty("output_extensions")
        private String outputExtensions;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getStatusMessage() {
            return statusMessage;
        }

        public void setStatusMessage(String statusMessage) {
            this.statusMessage = statusMessage;
        }

        public String getTimer() {
            return timer;
        }

        public void setTimer(String timer) {
            this.timer = timer;
        }

        public Long getFilesize() {
            return filesize;
        }

        public void setFilesize(Long filesize) {
            this.filesize = filesize;
        }

        public Long getOutputFilesize() {
            return outputFilesize;
        }

        public void setOutputFilesize(Long outputFilesize) {
            this.outputFilesize = outputFilesize;
        }

        public Integer getOutputFilenumber() {
            return outputFilenumber;
        }

        public void setOutputFilenumber(Integer outputFilenumber) {
            this.outputFilenumber = outputFilenumber;
        }

        public String getDownloadFilename() {
            return downloadFilename;
        }

        public void setDownloadFilename(String downloadFilename) {
            this.downloadFilename = downloadFilename;
        }

        public String getOutputExtensions() {
            return outputExtensions;
        }

        public void setOutputExtensions(String outputExtensions) {
            this.outputExtensions = outputExtensions;
        }
    }

    /**
     * 文件信息
     */
    public static class FileInfo {
        @JsonProperty("server_filename")
        private String serverFilename;
        
        private String filename;
        private Integer rotate = 0;
        private String password;

        public FileInfo() {}

        public FileInfo(String serverFilename, String filename) {
            this.serverFilename = serverFilename;
            this.filename = filename;
        }

        public String getServerFilename() {
            return serverFilename;
        }

        public void setServerFilename(String serverFilename) {
            this.serverFilename = serverFilename;
        }

        public String getFilename() {
            return filename;
        }

        public void setFilename(String filename) {
            this.filename = filename;
        }

        public Integer getRotate() {
            return rotate;
        }

        public void setRotate(Integer rotate) {
            this.rotate = rotate;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}
