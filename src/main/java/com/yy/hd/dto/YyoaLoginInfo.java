package com.yy.hd.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2025/6/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YyoaLoginInfo {
    /**
     * yy号
     */
    private Long yy;
    private String passport;
    private String email;
    /**
     * 过期时间，毫秒
     */
    private Long ts;

    /** 1-yy员工，2-外包员工，当identityType为2时（即身份为外包员工），“passport”和“yy”这两个字段没有值。 */
    private Integer identityType;
}
