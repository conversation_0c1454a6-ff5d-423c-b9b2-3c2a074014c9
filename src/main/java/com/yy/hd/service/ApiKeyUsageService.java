package com.yy.hd.service;

import com.yy.hd.entity.ApiKeyUsage;
import com.yy.hd.mapper.ApiKeyUsageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API Key使用记录服务
 * <AUTHOR> 2025/6/13
 */
@Slf4j
@Service
public class ApiKeyUsageService {
    
    @Autowired
    private ApiKeyUsageMapper apiKeyUsageMapper;
    
    /**
     * 记录API Key使用情况
     * @param apiKey 原始API Key
     * @param serviceType 服务类型
     * @param usageMonth 使用月份
     */
    @Transactional
    public void recordUsage(String apiKey, String serviceType, String usageMonth) {
        try {
            String apiKeyHash = hashApiKey(apiKey);
            LocalDateTime now = LocalDateTime.now();
            
            // 查询是否已存在记录
            ApiKeyUsage existingUsage = apiKeyUsageMapper.findByKeyAndServiceAndMonth(apiKeyHash, serviceType, usageMonth);
            
            if (existingUsage != null) {
                // 更新使用次数
                int updated = apiKeyUsageMapper.incrementUsage(apiKeyHash, serviceType, usageMonth, now, now);
                if (updated > 0) {
                    log.debug("Updated API key usage: service={}, month={}, hash={}", serviceType, usageMonth, maskHash(apiKeyHash));
                }
            } else {
                // 创建新记录
                ApiKeyUsage newUsage = new ApiKeyUsage();
                newUsage.setApiKeyHash(apiKeyHash);
                newUsage.setServiceType(serviceType);
                newUsage.setUsageMonth(usageMonth);
                newUsage.setUsageCount(1);
                newUsage.setLastUsedTime(now);
                newUsage.setCreatedTime(now);
                newUsage.setUpdatedTime(now);
                
                int inserted = apiKeyUsageMapper.insert(newUsage);
                if (inserted > 0) {
                    log.debug("Created new API key usage record: service={}, month={}, hash={}", serviceType, usageMonth, maskHash(apiKeyHash));
                }
            }
        } catch (Exception e) {
            log.error("Failed to record API key usage: service={}, month={}, error={}", serviceType, usageMonth, e.getMessage(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }
    
    /**
     * 获取API Key的使用次数
     * @param apiKey 原始API Key
     * @param serviceType 服务类型
     * @param usageMonth 使用月份
     * @return 使用次数
     */
    public int getUsageCount(String apiKey, String serviceType, String usageMonth) {
        try {
            String apiKeyHash = hashApiKey(apiKey);
            Integer count = apiKeyUsageMapper.getUsageCount(apiKeyHash, serviceType, usageMonth);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("Failed to get API key usage count: service={}, month={}, error={}", serviceType, usageMonth, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 获取指定服务和月份的所有使用记录
     * @param serviceType 服务类型
     * @param usageMonth 使用月份
     * @return 使用记录列表
     */
    public List<ApiKeyUsage> getUsageByServiceAndMonth(String serviceType, String usageMonth) {
        try {
            return apiKeyUsageMapper.findByServiceAndMonth(serviceType, usageMonth);
        } catch (Exception e) {
            log.error("Failed to get usage by service and month: service={}, month={}, error={}", serviceType, usageMonth, e.getMessage(), e);
            return List.of();
        }
    }
    
    /**
     * 重置指定月份的使用统计
     * @param serviceType 服务类型
     * @param usageMonth 使用月份
     */
    @Transactional
    public void resetUsageStats(String serviceType, String usageMonth) {
        try {
            int deleted = apiKeyUsageMapper.deleteByServiceAndMonth(serviceType, usageMonth);
            log.info("Reset usage stats: service={}, month={}, deleted={} records", serviceType, usageMonth, deleted);
        } catch (Exception e) {
            log.error("Failed to reset usage stats: service={}, month={}, error={}", serviceType, usageMonth, e.getMessage(), e);
            throw new RuntimeException("重置使用统计失败", e);
        }
    }
    
    /**
     * 对API Key进行哈希处理
     * @param apiKey 原始API Key
     * @return 哈希值
     */
    private String hashApiKey(String apiKey) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(apiKey.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            log.error("Failed to hash API key", e);
            // 使用简单的哈希作为备选方案
            return String.valueOf(apiKey.hashCode());
        }
    }
    
    /**
     * 掩码哈希值，用于日志输出
     * @param hash 哈希值
     * @return 掩码后的哈希值
     */
    private String maskHash(String hash) {
        if (hash == null || hash.length() <= 8) {
            return "****";
        }
        return hash.substring(0, 4) + "****" + hash.substring(hash.length() - 4);
    }
}
