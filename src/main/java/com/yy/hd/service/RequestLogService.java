package com.yy.hd.service;

import com.yy.hd.dto.YyoaLoginInfo;
import com.yy.hd.entity.RequestLog;
import com.yy.hd.mapper.RequestLogMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 请求日志服务
 * <AUTHOR> 2025/6/13
 */
@Slf4j
@Service
public class RequestLogService {
    
    @Autowired
    private RequestLogMapper requestLogMapper;
    
    /**
     * 记录请求日志
     * @param request HTTP请求
     * @param loginInfo 登录信息
     * @param originalSize 原始图片大小
     * @param compressedSize 压缩后图片大小
     * @param compressionMethod 压缩方法
     * @param processingTimeMs 处理时间
     * @param success 是否成功
     * @param errorMessage 错误信息
     * @return 请求ID
     */
    public String logRequest(HttpServletRequest request, YyoaLoginInfo loginInfo, 
                           Long originalSize, Long compressedSize, String compressionMethod,
                           Long processingTimeMs, Boolean success, String errorMessage) {
        try {
            String requestId = UUID.randomUUID().toString();
            
            RequestLog requestLog = new RequestLog();
            requestLog.setRequestId(requestId);
            
            // 设置用户信息
            if (loginInfo != null) {
                requestLog.setUserEmail(loginInfo.getEmail());
                requestLog.setUserPassport(loginInfo.getPassport());
                requestLog.setUserYy(loginInfo.getYy() != null ? loginInfo.getYy().toString() : null);
            }
            
            // 设置请求信息
            requestLog.setRequestUri(request.getRequestURI());
            requestLog.setRequestMethod(request.getMethod());
            requestLog.setUserAgent(request.getHeader("User-Agent"));
            requestLog.setClientIp(getClientIpAddress(request));
            
            // 设置图片信息
            requestLog.setImageOriginalSize(originalSize);
            requestLog.setImageCompressedSize(compressedSize);
            requestLog.setCompressionMethod(compressionMethod);
            
            // 计算压缩比例
            if (originalSize != null && compressedSize != null && originalSize > 0) {
                BigDecimal ratio = BigDecimal.valueOf(compressedSize)
                    .divide(BigDecimal.valueOf(originalSize), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                requestLog.setCompressionRatio(ratio);
            }
            
            // 设置处理信息
            requestLog.setProcessingTimeMs(processingTimeMs);
            requestLog.setSuccess(success);
            requestLog.setErrorMessage(errorMessage);
            requestLog.setCreatedTime(LocalDateTime.now());
            
            // 插入数据库
            int inserted = requestLogMapper.insert(requestLog);
            if (inserted > 0) {
                log.debug("Request logged successfully: requestId={}, user={}, method={}, uri={}, success={}", 
                    requestId, loginInfo != null ? loginInfo.getEmail() : "anonymous", 
                    compressionMethod, request.getRequestURI(), success);
            }
            
            return requestId;
        } catch (Exception e) {
            log.error("Failed to log request: uri={}, method={}, error={}", 
                request.getRequestURI(), compressionMethod, e.getMessage(), e);
            // 不抛出异常，避免影响主业务流程
            return null;
        }
    }
    
    /**
     * 记录简单的请求日志（不包含图片信息）
     * @param request HTTP请求
     * @param loginInfo 登录信息
     * @param success 是否成功
     * @param errorMessage 错误信息
     * @return 请求ID
     */
    public String logSimpleRequest(HttpServletRequest request, YyoaLoginInfo loginInfo, 
                                 Boolean success, String errorMessage) {
        return logRequest(request, loginInfo, null, null, null, null, success, errorMessage);
    }
    
    /**
     * 获取客户端真实IP地址
     * @param request HTTP请求
     * @return 客户端IP
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // 多个IP时取第一个
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (StringUtils.hasText(proxyClientIp) && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }
        
        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtils.hasText(wlProxyClientIp) && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }
        
        String httpClientIp = request.getHeader("HTTP_CLIENT_IP");
        if (StringUtils.hasText(httpClientIp) && !"unknown".equalsIgnoreCase(httpClientIp)) {
            return httpClientIp;
        }
        
        String httpXForwardedFor = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (StringUtils.hasText(httpXForwardedFor) && !"unknown".equalsIgnoreCase(httpXForwardedFor)) {
            return httpXForwardedFor;
        }
        
        return request.getRemoteAddr();
    }
}
