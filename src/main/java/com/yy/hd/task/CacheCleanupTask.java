package com.yy.hd.task;

import com.yy.hd.util.ImageCacheManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 缓存清理定时任务
 * <AUTHOR> 2025/6/5
 */
@Component
public class CacheCleanupTask {

    private static final Logger logger = LoggerFactory.getLogger(CacheCleanupTask.class);

    @Autowired
    private ImageCacheManager cacheManager;
    
    /**
     * 每天凌晨2点清理过期缓存
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredCache() {
        logger.info("开始定时清理过期缓存...");
        cacheManager.cleanExpiredCache();
        logger.info("定时清理过期缓存完成");
    }
}
