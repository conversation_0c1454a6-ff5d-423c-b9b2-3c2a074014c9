package com.yy.hd.web;

import com.yy.hd.config.ILoveApiKeyManager;
import com.yy.hd.config.TinyPngKeyManager;
import com.yy.hd.dto.YyoaLoginInfo;
import com.yy.hd.service.RequestLogService;
import com.yy.hd.util.ILoveApiUtil;
import com.yy.hd.util.ImageCacheManager;
import com.yy.hd.util.ImageUtil;
import com.yy.hd.util.TinyPngUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

/**
 * 图片处理控制器，支持缓存
 * <AUTHOR> 2025/5/6
 */
@RestController
@RequestMapping("/api/image")
public class ImageController {

    @Autowired
    private TinyPngUtil tinyPngUtil;

    @Autowired
    private ImageCacheManager cacheManager;

    @Autowired
    private TinyPngKeyManager keyManager;

    @Autowired
    private ILoveApiUtil iLoveApiUtil;

    @Autowired
    private ILoveApiKeyManager iLoveApiKeyManager;

    @Autowired
    private ImageUtil imageUtil;

    @Autowired
    private RequestLogService requestLogService;

    @PostMapping("/compress")
    public void compress(HttpServletRequest request, MultipartFile file, HttpServletResponse response) throws IOException {
        long startTime = System.currentTimeMillis();
        YyoaLoginInfo loginInfo = (YyoaLoginInfo) request.getAttribute("loginInfo");

        try {
            // 根据输入图片类型和压缩工具设置响应头
            setResponseHeaders(file, response, "tinypng");

            // 设置缓存控制头
            response.setHeader("Cache-Control", "public, max-age=86400"); // 缓存1天

            // 记录原始文件大小
            long originalSize = file.getSize();

            // 使用支持缓存的压缩方法
            tinyPngUtil.compress(file.getInputStream(), response.getOutputStream());
            response.flushBuffer();

            // 记录成功的请求日志
            long processingTime = System.currentTimeMillis() - startTime;
            requestLogService.logRequest(request, loginInfo, originalSize, null, "tinypng",
                processingTime, true, null);

        } catch (Exception e) {
            // 记录失败的请求日志
            long processingTime = System.currentTimeMillis() - startTime;
            requestLogService.logRequest(request, loginInfo, file.getSize(), null, "tinypng",
                processingTime, false, e.getMessage());
            throw e;
        }
    }

    @PostMapping("/compress/imageutil")
    public void compressWithImageUtil(HttpServletRequest request, MultipartFile file, HttpServletResponse response) throws IOException {
        long startTime = System.currentTimeMillis();
        YyoaLoginInfo loginInfo = (YyoaLoginInfo) request.getAttribute("loginInfo");

        try {
            // ImageUtil可能输出PNG或JPEG，根据优化策略决定
            setResponseHeaders(file, response, "imageutil");

            // 记录原始文件大小
            long originalSize = file.getSize();

            // 使用ImageUtil进行压缩（带缓存）
            imageUtil.compressWithCache(file.getInputStream(), response.getOutputStream());
            response.flushBuffer();

            // 记录成功的请求日志
            long processingTime = System.currentTimeMillis() - startTime;
            requestLogService.logRequest(request, loginInfo, originalSize, null, "imageutil",
                processingTime, true, null);

        } catch (Exception e) {
            // 记录失败的请求日志
            long processingTime = System.currentTimeMillis() - startTime;
            requestLogService.logRequest(request, loginInfo, file.getSize(), null, "imageutil",
                processingTime, false, e.getMessage());
            throw e;
        }
    }

    @PostMapping("/compress/iloveapi")
    public void compressWithILoveApi(HttpServletRequest request, MultipartFile file, HttpServletResponse response) throws IOException {
        long startTime = System.currentTimeMillis();
        YyoaLoginInfo loginInfo = (YyoaLoginInfo) request.getAttribute("loginInfo");

        try {
            // 根据输入图片类型设置响应头
            setResponseHeaders(file, response, "iloveapi");

            // 设置缓存控制头
            response.setHeader("Cache-Control", "public, max-age=86400"); // 缓存1天

            // 记录原始文件大小
            long originalSize = file.getSize();

            // 使用iLoveAPI进行异步压缩
            iLoveApiUtil.compress(file.getInputStream(), response.getOutputStream());
            response.flushBuffer();

            // 记录成功的请求日志
            long processingTime = System.currentTimeMillis() - startTime;
            requestLogService.logRequest(request, loginInfo, originalSize, null, "iloveapi",
                processingTime, true, null);

        } catch (Exception e) {
            // 记录失败的请求日志
            long processingTime = System.currentTimeMillis() - startTime;
            requestLogService.logRequest(request, loginInfo, file.getSize(), null, "iloveapi",
                processingTime, false, e.getMessage());
            throw e;
        }
    }

    @PostMapping("/preview/tinypng")
    public void previewTinyPng(HttpServletRequest request, MultipartFile file, HttpServletResponse response) throws IOException {
        // 设置预览响应头
        setPreviewResponseHeaders(file, response, "tinypng");

        // 使用TinyPNG进行压缩
        tinyPngUtil.compress(file.getInputStream(), response.getOutputStream());

        response.flushBuffer();
    }

    @PostMapping("/preview/iloveapi")
    public void previewILoveApi(HttpServletRequest request, MultipartFile file, HttpServletResponse response) throws IOException {
        // 设置预览响应头
        setPreviewResponseHeaders(file, response, "iloveapi");

        // 使用iLoveAPI进行压缩
        iLoveApiUtil.compress(file.getInputStream(), response.getOutputStream());

        response.flushBuffer();
    }

    @PostMapping("/preview/imageutil")
    public void previewImageUtil(HttpServletRequest request, MultipartFile file, HttpServletResponse response) throws IOException {
        // 设置预览响应头
        setPreviewResponseHeaders(file, response, "imageutil");

        // 使用ImageUtil进行压缩（带缓存）
        imageUtil.compressWithCache(file.getInputStream(), response.getOutputStream());

        response.flushBuffer();
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    public String getCacheStats() {
        return cacheManager.getCacheStats();
    }

    /**
     * 清理过期缓存
     */
    @PostMapping("/cache/clean")
    public String cleanCache() {
        cacheManager.cleanExpiredCache();
        return "缓存清理完成";
    }

    /**
     * 获取TinyPNG API key使用统计
     */
    @GetMapping("/tinypng/stats")
    public String getTinyPngStats() {
        return keyManager.getUsageStats();
    }

    /**
     * 重置当前月份的API key使用统计
     */
    @PostMapping("/tinypng/reset-stats")
    public String resetTinyPngStats() {
        keyManager.resetCurrentMonthStats();
        return "API key使用统计已重置";
    }

    /**
     * 检查是否有可用的API key
     */
    @GetMapping("/tinypng/available")
    public String checkAvailableKeys() {
        boolean hasAvailable = keyManager.hasAvailableKey();
        return hasAvailable ? "有可用的API key" : "所有API key都已达到月使用限制";
    }

    /**
     * 获取iLoveAPI公钥使用统计
     */
    @GetMapping("/iloveapi/stats")
    public String getILoveApiStats() {
        return iLoveApiKeyManager.getUsageStats();
    }

    /**
     * 重置当前月份的iLoveAPI公钥使用统计
     */
    @PostMapping("/iloveapi/reset-stats")
    public String resetILoveApiStats() {
        iLoveApiKeyManager.resetCurrentMonthStats();
        return "iLoveAPI公钥使用统计已重置";
    }

    /**
     * 检查是否有可用的iLoveAPI公钥
     */
    @GetMapping("/iloveapi/available")
    public String checkAvailableILoveApiKeys() {
        boolean hasAvailable = iLoveApiKeyManager.hasAvailableKey();
        return hasAvailable ? "有可用的iLoveAPI公钥" : "所有iLoveAPI公钥都已达到月使用限制";
    }

    /**
     * 根据图片类型和压缩工具设置响应头
     */
    private void setResponseHeaders(MultipartFile file, HttpServletResponse response, String compressionMethod) {
        String originalFilename = file.getOriginalFilename();
        String contentType = file.getContentType();

        // 确定输出格式和MIME类型
        String outputContentType;
        String outputExtension;
        String outputFilename;

        // 根据压缩方法确定输出格式
        switch (compressionMethod.toLowerCase()) {
            case "tinypng":
                // TinyPNG通常保持原格式
                if (contentType != null) {
                    switch (contentType.toLowerCase()) {
                        case "image/jpeg":
                        case "image/jpg":
                            outputContentType = MediaType.IMAGE_JPEG_VALUE;
                            outputExtension = "jpg";
                            break;
                        case "image/png":
                            outputContentType = MediaType.IMAGE_PNG_VALUE;
                            outputExtension = "png";
                            break;
                        case "image/webp":
                            outputContentType = "image/webp";
                            outputExtension = "webp";
                            break;
                        default:
                            outputContentType = MediaType.IMAGE_PNG_VALUE;
                            outputExtension = "png";
                            break;
                    }
                } else {
                    outputContentType = determineContentTypeFromFilename(originalFilename);
                    outputExtension = getExtensionFromContentType(outputContentType);
                }
                break;

            case "imageutil":
                // ImageUtil可能输出PNG或JPEG，根据优化策略决定
                // 通常输出PNG，但对于无透明度的图片可能输出JPEG
                if (contentType != null && (contentType.toLowerCase().contains("jpeg") || contentType.toLowerCase().contains("jpg"))) {
                    // 对于JPEG输入，可能保持JPEG格式
                    outputContentType = MediaType.IMAGE_JPEG_VALUE;
                    outputExtension = "jpg";
                } else {
                    // 默认输出PNG
                    outputContentType = MediaType.IMAGE_PNG_VALUE;
                    outputExtension = "png";
                }
                break;

            case "iloveapi":
                // iLoveAPI通常保持原格式
                if (contentType != null) {
                    switch (contentType.toLowerCase()) {
                        case "image/jpeg":
                        case "image/jpg":
                            outputContentType = MediaType.IMAGE_JPEG_VALUE;
                            outputExtension = "jpg";
                            break;
                        case "image/png":
                            outputContentType = MediaType.IMAGE_PNG_VALUE;
                            outputExtension = "png";
                            break;
                        case "image/webp":
                            outputContentType = "image/webp";
                            outputExtension = "webp";
                            break;
                        default:
                            outputContentType = MediaType.IMAGE_PNG_VALUE;
                            outputExtension = "png";
                            break;
                    }
                } else {
                    outputContentType = determineContentTypeFromFilename(originalFilename);
                    outputExtension = getExtensionFromContentType(outputContentType);
                }
                break;

            default:
                // 默认情况
                outputContentType = MediaType.IMAGE_PNG_VALUE;
                outputExtension = "png";
                break;
        }

        // 生成输出文件名
        outputFilename = UUID.randomUUID().toString().replaceAll("-", "") +"." + outputExtension;

        // 设置响应头
        response.setContentType(outputContentType);
        response.setHeader("Content-Disposition", "inline; filename=" + outputFilename);
    }

    /**
     * 设置预览响应头（用于预览接口）
     */
    private void setPreviewResponseHeaders(MultipartFile file, HttpServletResponse response, String compressionMethod) {
        String originalFilename = file.getOriginalFilename();
        String contentType = file.getContentType();

        // 确定输出格式和MIME类型
        String outputContentType;
        String outputExtension;
        String outputFilename;

        // 根据压缩方法确定输出格式
        switch (compressionMethod.toLowerCase()) {
            case "tinypng":
            case "iloveapi":
                // TinyPNG和iLoveAPI通常保持原格式
                if (contentType != null) {
                    switch (contentType.toLowerCase()) {
                        case "image/jpeg":
                        case "image/jpg":
                            outputContentType = MediaType.IMAGE_JPEG_VALUE;
                            outputExtension = "jpg";
                            break;
                        case "image/png":
                            outputContentType = MediaType.IMAGE_PNG_VALUE;
                            outputExtension = "png";
                            break;
                        case "image/webp":
                            outputContentType = "image/webp";
                            outputExtension = "webp";
                            break;
                        default:
                            outputContentType = MediaType.IMAGE_PNG_VALUE;
                            outputExtension = "png";
                            break;
                    }
                } else {
                    outputContentType = determineContentTypeFromFilename(originalFilename);
                    outputExtension = getExtensionFromContentType(outputContentType);
                }
                break;

            case "imageutil":
                // ImageUtil可能输出PNG或JPEG
                if (contentType != null && (contentType.toLowerCase().contains("jpeg") || contentType.toLowerCase().contains("jpg"))) {
                    outputContentType = MediaType.IMAGE_JPEG_VALUE;
                    outputExtension = "jpg";
                } else {
                    outputContentType = MediaType.IMAGE_PNG_VALUE;
                    outputExtension = "png";
                }
                break;

            default:
                outputContentType = MediaType.IMAGE_PNG_VALUE;
                outputExtension = "png";
                break;
        }

        // 生成预览文件名
        String baseName = originalFilename != null ? removeFileExtension(originalFilename) : "preview";
        outputFilename = baseName + "_" + compressionMethod + "_preview." + outputExtension;

        // 设置响应头
        response.setContentType(outputContentType);
        response.setHeader("Content-Disposition", "inline; filename=" + outputFilename);
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 移除文件扩展名
     */
    private String removeFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }
        return filename.substring(0, lastDotIndex);
    }

    /**
     * 根据文件名确定Content-Type
     */
    private String determineContentTypeFromFilename(String filename) {
        if (filename != null) {
            String extension = getFileExtension(filename).toLowerCase();
            switch (extension) {
                case "jpg":
                case "jpeg":
                    return MediaType.IMAGE_JPEG_VALUE;
                case "png":
                    return MediaType.IMAGE_PNG_VALUE;
                case "webp":
                    return "image/webp";
                default:
                    return MediaType.IMAGE_PNG_VALUE;
            }
        }
        return MediaType.IMAGE_PNG_VALUE;
    }

    /**
     * 根据Content-Type获取文件扩展名
     */
    private String getExtensionFromContentType(String contentType) {
        switch (contentType.toLowerCase()) {
            case "image/jpeg":
                return "jpg";
            case "image/png":
                return "png";
            case "image/webp":
                return "webp";
            default:
                return "png";
        }
    }

}
