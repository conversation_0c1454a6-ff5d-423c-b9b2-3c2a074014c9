package com.yy.hd.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> 2020/7/24
 */
@Component
public class CORSFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(CORSFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
            throws ServletException, IOException {

        //跨域
        if (CorsUtils.isCorsRequest(request)) {

            String origin = request.getHeader(HttpHeaders.ORIGIN);
            log.info("CORS filter origin:{}", origin);

//            boolean allow = origin.endsWith(".yy.com") || origin.contains(".yy.com:") || origin.endsWith(".figma.com");

            boolean allow = true;

            if (allow) {
                //通过
                response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
                response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, Boolean.TRUE.toString());
                if (CorsUtils.isPreFlightRequest(request)) {
                    //OPTIONS
                    response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, request.getHeader(HttpHeaders.ACCESS_CONTROL_REQUEST_METHOD));
                    response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, request.getHeader(HttpHeaders.ACCESS_CONTROL_REQUEST_HEADERS));
                    return;
                }
                filterChain.doFilter(request, response);

            } else {
                //拒绝
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.getOutputStream().write("Invalid CORS request".getBytes(StandardCharsets.UTF_8));
                response.flushBuffer();
            }

        } else {
            filterChain.doFilter(request, response);
        }
    }

}
