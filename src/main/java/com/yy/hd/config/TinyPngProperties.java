package com.yy.hd.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TinyPNG配置属性类
 * <AUTHOR> 2025/6/5
 */
@Component
@ConfigurationProperties(prefix = "tinypng")
public class TinyPngProperties {

    /**
     * API密钥列表
     */
    private List<String> apiKeys;

    /**
     * 每个key的月使用限制
     */
    private int monthlyLimit = 500;

    /**
     * 是否启用key轮换
     */
    private boolean rotationEnabled = true;

    public List<String> getApiKeys() {
        return apiKeys;
    }

    public void setApiKeys(List<String> apiKeys) {
        this.apiKeys = apiKeys;
    }

    public int getMonthlyLimit() {
        return monthlyLimit;
    }

    public void setMonthlyLimit(int monthlyLimit) {
        this.monthlyLimit = monthlyLimit;
    }

    public boolean isRotationEnabled() {
        return rotationEnabled;
    }

    public void setRotationEnabled(boolean rotationEnabled) {
        this.rotationEnabled = rotationEnabled;
    }
}
