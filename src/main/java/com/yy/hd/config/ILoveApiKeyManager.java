package com.yy.hd.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * iLoveAPI公钥管理器
 * 负责管理多个公钥的轮换使用和使用量统计
 * <AUTHOR> 2025/6/5
 */
@Component
public class ILoveApiKeyManager {

    private static final Logger logger = LoggerFactory.getLogger(ILoveApiKeyManager.class);

    @Autowired
    private ILoveApiProperties properties;

    // 当前使用的key索引
    private final AtomicInteger currentKeyIndex = new AtomicInteger(0);
    
    // 每个key的使用统计 - key: "publicKey-YYYY-MM", value: 使用次数
    private final Map<String, AtomicInteger> keyUsageStats = new ConcurrentHashMap<>();

    /**
     * 获取下一个可用的公钥
     * @return 可用的公钥，如果所有key都达到限制则返回null
     */
    public synchronized String getNextAvailableKey() {
        List<String> publicKeys = properties.getPublicKeys();
        
        if (publicKeys == null || publicKeys.isEmpty()) {
            logger.error("未配置iLoveAPI公钥");
            return null;
        }

        // 如果只有一个key或者未启用轮换，直接返回第一个
        if (publicKeys.size() == 1 || !properties.isRotationEnabled()) {
            String key = publicKeys.get(0);
            recordKeyUsage(key);
            return key;
        }

        // 查找可用的key（未达到月限制的）
        String currentMonth = getCurrentMonth();
        int startIndex = currentKeyIndex.get();
        
        for (int i = 0; i < publicKeys.size(); i++) {
            int keyIndex = (startIndex + i) % publicKeys.size();
            String publicKey = publicKeys.get(keyIndex);
            String usageKey = publicKey + "-" + currentMonth;
            
            int currentUsage = keyUsageStats.computeIfAbsent(usageKey, k -> new AtomicInteger(0)).get();
            
            if (currentUsage < properties.getMonthlyLimit()) {
                // 找到可用的key，更新索引并记录使用
                currentKeyIndex.set((keyIndex + 1) % publicKeys.size());
                recordKeyUsage(publicKey);
                
                logger.info("使用iLoveAPI公钥索引: {}, 本月已使用: {}/{}", 
                    keyIndex, currentUsage + 1, properties.getMonthlyLimit());
                
                return publicKey;
            }
        }

        // 所有key都达到限制
        logger.error("所有iLoveAPI公钥都已达到月使用限制 ({}次)", properties.getMonthlyLimit());
        return null;
    }

    /**
     * 记录公钥使用情况
     * @param publicKey 使用的公钥
     */
    private void recordKeyUsage(String publicKey) {
        String currentMonth = getCurrentMonth();
        String usageKey = publicKey + "-" + currentMonth;
        
        int newUsage = keyUsageStats.computeIfAbsent(usageKey, k -> new AtomicInteger(0)).incrementAndGet();
        
        // 当使用量接近限制时发出警告
        int monthlyLimit = properties.getMonthlyLimit();
        if (newUsage >= monthlyLimit * 0.9) { // 90%时警告
            logger.warn("iLoveAPI公钥使用量警告: 本月已使用 {}/{} 次", newUsage, monthlyLimit);
        }
    }

    /**
     * 获取当前月份字符串 (YYYY-MM格式)
     * @return 当前月份
     */
    private String getCurrentMonth() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 获取所有公钥的使用统计
     * @return 使用统计信息
     */
    public String getUsageStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== iLoveAPI公钥使用统计 ===\n");
        
        List<String> publicKeys = properties.getPublicKeys();
        if (publicKeys == null || publicKeys.isEmpty()) {
            stats.append("未配置公钥\n");
            return stats.toString();
        }
        
        String currentMonth = getCurrentMonth();
        
        for (int i = 0; i < publicKeys.size(); i++) {
            String publicKey = publicKeys.get(i);
            String maskedKey = maskPublicKey(publicKey);
            String usageKey = publicKey + "-" + currentMonth;
            
            int usage = keyUsageStats.computeIfAbsent(usageKey, k -> new AtomicInteger(0)).get();
            int limit = properties.getMonthlyLimit();
            
            stats.append(String.format("Key %d (%s): %d/%d 次 (%.1f%%)\n", 
                i + 1, maskedKey, usage, limit, (usage * 100.0 / limit)));
        }
        
        stats.append("当前月份: ").append(currentMonth).append("\n");
        stats.append("轮换启用: ").append(properties.isRotationEnabled() ? "是" : "否");
        
        return stats.toString();
    }

    /**
     * 重置指定月份的使用统计（用于测试或手动重置）
     * @param month 月份 (YYYY-MM格式)
     */
    public void resetUsageStats(String month) {
        List<String> publicKeys = properties.getPublicKeys();
        if (publicKeys == null) return;
        
        for (String publicKey : publicKeys) {
            String usageKey = publicKey + "-" + month;
            keyUsageStats.remove(usageKey);
        }
        
        logger.info("已重置iLoveAPI {} 月份的公钥使用统计", month);
    }

    /**
     * 重置当前月份的使用统计
     */
    public void resetCurrentMonthStats() {
        resetUsageStats(getCurrentMonth());
    }

    /**
     * 掩码公钥，只显示前8位和后8位
     * @param publicKey 原始公钥
     * @return 掩码后的公钥
     */
    private String maskPublicKey(String publicKey) {
        if (publicKey == null || publicKey.length() <= 16) {
            return "****";
        }
        
        return publicKey.substring(0, 8) + "****" + publicKey.substring(publicKey.length() - 8);
    }

    /**
     * 检查是否有可用的公钥
     * @return 是否有可用的key
     */
    public boolean hasAvailableKey() {
        List<String> publicKeys = properties.getPublicKeys();
        
        if (publicKeys == null || publicKeys.isEmpty()) {
            return false;
        }

        String currentMonth = getCurrentMonth();
        
        for (String publicKey : publicKeys) {
            String usageKey = publicKey + "-" + currentMonth;
            int currentUsage = keyUsageStats.computeIfAbsent(usageKey, k -> new AtomicInteger(0)).get();
            
            if (currentUsage < properties.getMonthlyLimit()) {
                return true;
            }
        }
        
        return false;
    }
}
