package com.yy.hd.config;

import com.tinify.Tinify;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * TinyPNG配置类
 * 支持多个API key轮流使用
 * <AUTHOR> 2025/6/5
 */
@Configuration
public class TinyPngConfig {

    private static final Logger logger = LoggerFactory.getLogger(TinyPngConfig.class);

    @Autowired
    private TinyPngProperties tinyPngProperties;

    @PostConstruct
    public void init() {
        // 验证配置
        if (tinyPngProperties.getApiKeys() == null || tinyPngProperties.getApiKeys().isEmpty()) {
            logger.error("未配置TinyPNG API keys，请在application.yml中配置tinypng.api-keys");
            return;
        }

        logger.info("TinyPNG配置初始化完成:");
        logger.info("- 配置了 {} 个API keys", tinyPngProperties.getApiKeys().size());
        logger.info("- 月使用限制: {} 次/key", tinyPngProperties.getMonthlyLimit());
        logger.info("- 轮换启用: {}", tinyPngProperties.isRotationEnabled() ? "是" : "否");

        // 设置第一个key作为默认key（兼容性考虑）
        String firstKey = tinyPngProperties.getApiKeys().get(0);
        Tinify.setKey(firstKey);
        logger.info("默认API key已设置");
    }

}
