package com.yy.hd.config;

import com.alibaba.fastjson.JSON;
import com.yy.hd.dto.YyoaLoginInfo;
import com.yy.hd.service.RequestLogService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * <AUTHOR> 2025/6/13
 */
@Slf4j
@Component
public class LoginFilter extends OncePerRequestFilter {

    @Autowired
    private RequestLogService requestLogService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        long startTime = System.currentTimeMillis();

        String figmaIdentityInfo = request.getHeader("figma-identity-info");
        String figmaIdentitySign = request.getHeader("figma-identity-sign");
        YyoaLoginInfo loginInfo = getYyoaLoginInfo(figmaIdentityInfo, figmaIdentitySign);
        log.info("doFilterInternal loginInfo:{}", loginInfo);

        if (!"/api/image/compress".equals(request.getRequestURI())) {
            filterChain.doFilter(request, response);
            return;
        }

        // 权限验证
        if (!isAuthorized(loginInfo)) {
            log.warn("Unauthorized access attempt - loginInfo:{}", loginInfo);

            // 记录未授权访问日志
            long processingTime = System.currentTimeMillis() - startTime;
            requestLogService.logRequest(request, loginInfo, null, null, null,
                processingTime, false, "无权限访问");

            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"error\":\"无权限访问\",\"code\":401}");
            return;
        }

        // 将loginInfo存储到request attribute中，供后续使用
        request.setAttribute("loginInfo", loginInfo);

        filterChain.doFilter(request, response);
    }

    /**
     * 权限验证方法
     * 验证loginInfo是否满足权限要求：
     * 1. loginInfo不为空
     * 2. email以yy.com结尾
     * 3. passport以dw_开头
     * 4. yy以9090开头
     *
     * @param loginInfo 登录信息
     * @return true-有权限，false-无权限
     */
    private boolean isAuthorized(YyoaLoginInfo loginInfo) {
        if (loginInfo == null) {
            log.warn("Authorization failed: loginInfo is null");
            return false;
        }

        // 检查email是否以yy.com结尾
        String email = loginInfo.getEmail();
        if (email == null || !email.endsWith("yy.com")) {
            log.warn("Authorization failed: email does not end with yy.com, email:{}", email);
            return false;
        }

        // 检查passport是否以dw_开头
        String passport = loginInfo.getPassport();
        if (passport == null || !passport.startsWith("dw_")) {
            log.warn("Authorization failed: passport does not start with dw_, passport:{}", passport);
            return false;
        }

        // 检查yy是否以9090开头
        Long yy = loginInfo.getYy();
        if (yy == null || !yy.toString().startsWith("9090")) {
            log.warn("Authorization failed: yy does not start with 9090, yy:{}", yy);
            return false;
        }

        log.info("Authorization successful for user - email:{}, passport:{}, yy:{}", email, passport, yy);
        return true;
    }

    public static YyoaLoginInfo getYyoaLoginInfo(String figmaIdentityInfo, String figmaIdentitySign) {
        try {
            if (!StringUtils.hasLength(figmaIdentityInfo) && !StringUtils.hasLength(figmaIdentitySign)) {
                log.info("getYyoaLoginInfo empty header");
                return null;
            }
            byte[] content = figmaIdentityInfo.getBytes(StandardCharsets.UTF_8);
            if (verifyWithPublicKey(content, figmaIdentitySign)) {
                byte[] decodeInfo = Base64.getDecoder().decode(content);
                YyoaLoginInfo loginInfo = JSON.parseObject(decodeInfo, YyoaLoginInfo.class);
                boolean isLogin = loginInfo != null && loginInfo.getTs() != null;
                if (isLogin) {
                    log.info("getYyoaLoginInfo loginSuccess info:{}", new String(decodeInfo));
                    return loginInfo;
                }
            }
            log.info("getYyoaLoginInfo loginFailed figmaIdentityInfo:{} figmaIdentitySign:{}", figmaIdentityInfo, figmaIdentitySign);
            return null;
        } catch (Exception e) {
            log.warn("getYyoaLoginInfo error:{} info:{} sign:{}", e.getMessage(), figmaIdentityInfo, figmaIdentitySign);
            return null;
        }
    }

    /**
     * 使用公钥验签
     *
     * @param content           待验签内容, 对应 figmaIdentityInfo.getBytes(StandardCharsets.UTF_8)
     * @param figmaIdentitySign 签名。当前场景下为：figmaIdentitySign
     * @return true-验签通过
     */
    private static boolean verifyWithPublicKey(byte[] content, String figmaIdentitySign) {
        try {
            java.security.Signature signature = java.security.Signature.getInstance("MD5withRSA");
            signature.initVerify(PUBLIC_KEY);
            signature.update(content);

            final byte[] decode = Base64.getDecoder().decode(figmaIdentitySign.getBytes(StandardCharsets.UTF_8));
            return signature.verify(decode);
        } catch (Exception e) {
            log.warn("verifyWithPublicKey error:{} figmaIdentityInfo:{} figmaIdentitySign:{}", e.getMessage(), new String(content), figmaIdentitySign, e);
            return false;
        }
    }

    private static final PublicKey PUBLIC_KEY = loadPublicKey();

    private static PublicKey loadPublicKey() {
        try {
            String certificatePath = "certificate/yylive_figma.crt";
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            final InputStream certificateAsStream = new ClassPathResource(certificatePath).getInputStream();
            X509Certificate x509Certificate = (X509Certificate) certificateFactory.generateCertificate(certificateAsStream);
            return x509Certificate.getPublicKey();
        } catch (Exception e) {
            log.error("loadPublicKey error:{}", e.getMessage(), e);
            System.exit(1000);
            return null;
        }
    }

}
