package com.yy.hd.config;

import com.yy.hd.service.ApiKeyUsageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TinyPNG API Key管理器
 * 负责管理多个API key的轮换使用和使用量统计
 * <AUTHOR> 2025/6/5
 */
@Component
public class TinyPngKeyManager {

    private static final Logger logger = LoggerFactory.getLogger(TinyPngKeyManager.class);

    @Autowired
    private TinyPngProperties tinyPngProperties;

    @Autowired
    private ApiKeyUsageService apiKeyUsageService;

    // 当前使用的key索引
    private final AtomicInteger currentKeyIndex = new AtomicInteger(0);

    // 服务类型常量
    private static final String SERVICE_TYPE = "tinypng";

    /**
     * 获取下一个可用的API key
     * @return 可用的API key，如果所有key都达到限制则返回null
     */
    public synchronized String getNextAvailableKey() {
        List<String> apiKeys = tinyPngProperties.getApiKeys();
        
        if (apiKeys == null || apiKeys.isEmpty()) {
            logger.error("未配置TinyPNG API keys");
            return null;
        }

        // 如果只有一个key或者未启用轮换，直接返回第一个
        if (apiKeys.size() == 1 || !tinyPngProperties.isRotationEnabled()) {
            String key = apiKeys.get(0);
            recordKeyUsage(key);
            return key;
        }

        // 查找可用的key（未达到月限制的）
        String currentMonth = getCurrentMonth();
        int startIndex = currentKeyIndex.get();
        
        for (int i = 0; i < apiKeys.size(); i++) {
            int keyIndex = (startIndex + i) % apiKeys.size();
            String apiKey = apiKeys.get(keyIndex);

            int currentUsage = apiKeyUsageService.getUsageCount(apiKey, SERVICE_TYPE, currentMonth);

            if (currentUsage < tinyPngProperties.getMonthlyLimit()) {
                // 找到可用的key，更新索引并记录使用
                currentKeyIndex.set((keyIndex + 1) % apiKeys.size());
                recordKeyUsage(apiKey);

                logger.info("使用API key索引: {}, 本月已使用: {}/{}",
                    keyIndex, currentUsage + 1, tinyPngProperties.getMonthlyLimit());

                return apiKey;
            }
        }

        // 所有key都达到限制
        logger.error("所有TinyPNG API keys都已达到月使用限制 ({}次)", tinyPngProperties.getMonthlyLimit());
        return null;
    }

    /**
     * 记录API key使用情况
     * @param apiKey 使用的API key
     */
    private void recordKeyUsage(String apiKey) {
        String currentMonth = getCurrentMonth();

        // 使用数据库记录使用情况
        apiKeyUsageService.recordUsage(apiKey, SERVICE_TYPE, currentMonth);

        // 获取最新使用次数并检查是否需要警告
        int newUsage = apiKeyUsageService.getUsageCount(apiKey, SERVICE_TYPE, currentMonth);
        int monthlyLimit = tinyPngProperties.getMonthlyLimit();
        if (newUsage >= monthlyLimit * 0.9) { // 90%时警告
            logger.warn("API key使用量警告: 本月已使用 {}/{} 次", newUsage, monthlyLimit);
        }
    }

    /**
     * 获取当前月份字符串 (YYYY-MM格式)
     * @return 当前月份
     */
    private String getCurrentMonth() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }

    /**
     * 获取所有API key的使用统计
     * @return 使用统计信息
     */
    public String getUsageStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== TinyPNG API Key 使用统计 ===\n");

        List<String> apiKeys = tinyPngProperties.getApiKeys();
        String currentMonth = getCurrentMonth();

        for (int i = 0; i < apiKeys.size(); i++) {
            String apiKey = apiKeys.get(i);
            String maskedKey = maskApiKey(apiKey);

            int usage = apiKeyUsageService.getUsageCount(apiKey, SERVICE_TYPE, currentMonth);
            int limit = tinyPngProperties.getMonthlyLimit();

            stats.append(String.format("Key %d (%s): %d/%d 次 (%.1f%%)\n",
                i + 1, maskedKey, usage, limit, (usage * 100.0 / limit)));
        }

        stats.append("当前月份: ").append(currentMonth).append("\n");
        stats.append("轮换启用: ").append(tinyPngProperties.isRotationEnabled() ? "是" : "否");

        return stats.toString();
    }

    /**
     * 重置指定月份的使用统计（用于测试或手动重置）
     * @param month 月份 (YYYY-MM格式)
     */
    public void resetUsageStats(String month) {
        apiKeyUsageService.resetUsageStats(SERVICE_TYPE, month);
        logger.info("已重置 {} 月份的API key使用统计", month);
    }

    /**
     * 重置当前月份的使用统计
     */
    public void resetCurrentMonthStats() {
        resetUsageStats(getCurrentMonth());
    }

    /**
     * 掩码API key，只显示前4位和后4位
     * @param apiKey 原始API key
     * @return 掩码后的API key
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * 检查是否有可用的API key
     * @return 是否有可用的key
     */
    public boolean hasAvailableKey() {
        List<String> apiKeys = tinyPngProperties.getApiKeys();

        if (apiKeys == null || apiKeys.isEmpty()) {
            return false;
        }

        String currentMonth = getCurrentMonth();

        for (String apiKey : apiKeys) {
            int currentUsage = apiKeyUsageService.getUsageCount(apiKey, SERVICE_TYPE, currentMonth);

            if (currentUsage < tinyPngProperties.getMonthlyLimit()) {
                return true;
            }
        }

        return false;
    }
}
