package com.yy.hd.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 图片缓存配置类
 * <AUTHOR> 2025/6/5
 */
@Configuration
@ConfigurationProperties(prefix = "image.cache")
public class CacheConfig {
    
    /**
     * 缓存目录路径
     */
    private String directory = "./cache/images";
    
    /**
     * 缓存过期时间（天）
     */
    private int expireDays = 30;
    
    /**
     * 是否启用缓存
     */
    private boolean enabled = true;
    
    public String getDirectory() {
        return directory;
    }
    
    public void setDirectory(String directory) {
        this.directory = directory;
    }
    
    public int getExpireDays() {
        return expireDays;
    }
    
    public void setExpireDays(int expireDays) {
        this.expireDays = expireDays;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
