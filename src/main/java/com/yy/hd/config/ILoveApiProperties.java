package com.yy.hd.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * iLoveAPI配置属性类
 * 支持多个公钥轮换使用
 * <AUTHOR> 2025/6/5
 */
@Component
@ConfigurationProperties(prefix = "iloveapi")
public class ILoveApiProperties {

    /**
     * 公钥列表，支持多个key轮换使用
     */
    private List<String> publicKeys;

    /**
     * API基础URL
     */
    private String baseUrl = "https://api.ilovepdf.com/v1";

    /**
     * 连接超时时间（秒）
     */
    private int connectTimeout = 30;

    /**
     * 读取超时时间（秒）
     */
    private int readTimeout = 120;

    /**
     * 每个key的月使用限制
     */
    private int monthlyLimit = 500;

    /**
     * 是否启用key轮换
     */
    private boolean rotationEnabled = true;

    /**
     * 是否启用
     */
    private boolean enabled = true;

    public List<String> getPublicKeys() {
        return publicKeys;
    }

    public void setPublicKeys(List<String> publicKeys) {
        this.publicKeys = publicKeys;
    }

    public int getMonthlyLimit() {
        return monthlyLimit;
    }

    public void setMonthlyLimit(int monthlyLimit) {
        this.monthlyLimit = monthlyLimit;
    }

    public boolean isRotationEnabled() {
        return rotationEnabled;
    }

    public void setRotationEnabled(boolean rotationEnabled) {
        this.rotationEnabled = rotationEnabled;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
