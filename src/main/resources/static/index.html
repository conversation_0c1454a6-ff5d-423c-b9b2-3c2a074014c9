<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片压缩工具</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 10px;
            background-color: #f5f5f5;
            font-size: 16px;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        h1 {
            color: #333;
            text-align: center;
            font-size: 28px;
            margin-top: 5px;
            margin-bottom: 10px;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .upload-area {
            border: 3px dashed #ccc;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.active {
            border-color: #28a745;
            background-color: #f0fff0;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 18px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .preview-container {
            display: flex;
            flex-wrap: nowrap;
            gap: 20px;
            margin-top: 15px;
            flex: 1;
            overflow: hidden;
        }
        .preview-box {
            flex: 1;
            min-width: 0;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .preview-box h3 {
            margin-top: 0;
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            font-size: 20px;
        }
        .preview-image {
            max-width: 100%;
            height: auto;
            max-height: calc(100vh - 250px);
            object-fit: contain;
            display: block;
            margin: 0 auto;
            flex: 1;
        }
        .info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .hidden {
            display: none;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 6px solid rgba(0, 0, 0, 0.1);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border-left-color: #007bff;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片压缩工具</h1>

        <div id="uploadArea" class="upload-area">
            <p style="font-size: 20px;">拖放图片到这里或点击选择文件</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>

        <div class="text-center">
            <button id="compressBtn" class="btn" disabled>压缩图片</button>
            <a href="/compress-preview.html" class="btn" style="text-decoration: none; margin-left: 10px; background-color: #28a745;">🔍 多方法预览对比</a>
        </div>

        <div id="loading" class="loading hidden">
            <div class="spinner"></div>
            <p style="font-size: 18px;">正在压缩图片...</p>
        </div>

        <div id="previewContainer" class="preview-container hidden">
            <div class="preview-box">
                <h3>原始图片</h3>
                <img id="originalImage" class="preview-image">
                <div id="originalInfo" class="info"></div>
            </div>

            <div class="preview-box">
                <h3>压缩后图片</h3>
                <img id="compressedImage" class="preview-image">
                <div id="compressedInfo" class="info"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const compressBtn = document.getElementById('compressBtn');
            const loading = document.getElementById('loading');
            const previewContainer = document.getElementById('previewContainer');
            const originalImage = document.getElementById('originalImage');
            const compressedImage = document.getElementById('compressedImage');
            const originalInfo = document.getElementById('originalInfo');
            const compressedInfo = document.getElementById('compressedInfo');

            let selectedFile = null;

            // 点击上传区域触发文件选择
            uploadArea.addEventListener('click', function() {
                fileInput.click();
            });

            // 处理文件选择
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });

            // 拖放功能
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('active');
            });

            uploadArea.addEventListener('dragleave', function() {
                uploadArea.classList.remove('active');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('active');
                handleFiles(e.dataTransfer.files);
            });

            // 处理选择的文件
            function handleFiles(files) {
                if (files.length > 0) {
                    selectedFile = files[0];

                    if (!selectedFile.type.match('image.*')) {
                        alert('请选择图片文件！');
                        return;
                    }

                    // 显示原始图片预览
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        originalImage.src = e.target.result;
                        previewContainer.classList.remove('hidden');
                        compressBtn.disabled = false;

                        // 显示原始图片信息
                        getImageInfo(e.target.result, originalInfo, '原始大小');
                    };
                    reader.readAsDataURL(selectedFile);
                }
            }

            // 压缩按钮点击事件
            compressBtn.addEventListener('click', function() {
                if (!selectedFile) return;

                loading.classList.remove('hidden');
                compressBtn.disabled = true;

                const formData = new FormData();
                formData.append('file', selectedFile);

                fetch('/api/image/compress', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('压缩失败');
                    }
                    return response.blob();
                })
                .then(blob => {
                    const compressedUrl = URL.createObjectURL(blob);
                    compressedImage.src = compressedUrl;

                    // 显示压缩后图片信息
                    getImageInfo(compressedUrl, compressedInfo, '压缩后大小');

                    loading.classList.add('hidden');
                    compressBtn.disabled = false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('图片压缩失败: ' + error.message);
                    loading.classList.add('hidden');
                    compressBtn.disabled = false;
                });
            });

            // 获取图片信息
            function getImageInfo(dataUrl, infoElement, sizeLabel) {
                const img = new Image();
                img.onload = function() {
                    const width = this.width;
                    const height = this.height;

                    // 计算文件大小
                    let fileSize;
                    if (dataUrl.startsWith('blob:')) {
                        // 对于Blob URL，我们需要获取实际的Blob对象
                        fetch(dataUrl)
                            .then(response => response.blob())
                            .then(blob => {
                                updateInfoElement(infoElement, width, height, blob.size, sizeLabel);
                            });
                    } else {
                        // 对于Data URL，我们可以直接计算大小
                        const base64 = dataUrl.split(',')[1];
                        const binarySize = atob(base64).length;
                        updateInfoElement(infoElement, width, height, binarySize, sizeLabel);
                    }
                };
                img.src = dataUrl;
            }

            function updateInfoElement(element, width, height, size, sizeLabel) {
                const sizeInKB = (size / 1024).toFixed(2);

                // 计算压缩率，只在压缩后的图片中显示
                let compressionInfo = '';
                let sizeInfo = `${sizeLabel}: ${sizeInKB} KB`;

                if (sizeLabel.includes('压缩后') && originalInfo.dataset.size) {
                    const originalSize = parseFloat(originalInfo.dataset.size);
                    const compressedSize = size;
                    const ratio = ((1 - compressedSize / originalSize) * 100).toFixed(2);

                    // 如果是负数，说明文件反而变大了
                    if (ratio < 0) {
                        sizeInfo += ` <span style="color: #ff4500;">(增大了 ${Math.abs(ratio).toFixed(2)}%)</span>`;
                    } else if (ratio == 0) {
                        sizeInfo += ` <span style="color: #0066cc;">(原图已是8位或更低位深度)</span>`;
                    } else {
                        sizeInfo += ` <span style="color: #008000;">(压缩率: ${ratio}%)</span>`;
                    }
                }

                // 存储原始大小以便计算压缩率
                if (sizeLabel.includes('原始')) {
                    originalInfo.dataset.size = size;
                }

                element.innerHTML = `
                    <p>尺寸: ${width} x ${height} 像素</p>
                    <p>${sizeInfo}</p>
                `;
            }
        });
    </script>
</body>
</html>
