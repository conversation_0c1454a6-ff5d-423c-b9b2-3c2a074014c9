<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片压缩预览对比</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            font-size: 14px;
        }
        h1 {
            color: #333;
            text-align: center;
            font-size: 28px;
            margin-bottom: 20px;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .upload-section {
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 3px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.active {
            border-color: #28a745;
            background-color: #f0fff0;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0069d9;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .btn.secondary {
            background-color: #6c757d;
        }
        .btn.secondary:hover {
            background-color: #545b62;
        }
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .preview-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fafafa;
            position: relative;
        }
        .preview-card h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
            font-size: 18px;
        }
        .preview-image {
            width: 100%;
            max-height: 200px;
            object-fit: contain;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        .image-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        .compression-stats {
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin-top: 8px;
            font-size: 11px;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border-left-color: #007bff;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
        .download-btn {
            background-color: #28a745;
            font-size: 12px;
            padding: 6px 12px;
            margin-top: 8px;
        }
        .download-btn:hover {
            background-color: #218838;
        }
        .stats-summary {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #2196f3;
        }
        .best-compression {
            border: 2px solid #28a745;
            background-color: #f8fff8;
        }
        .best-compression h3 {
            border-bottom-color: #28a745;
            color: #28a745;
        }
        .error-card {
            background-color: #fff5f5;
            border-color: #f56565;
        }
        .error-card h3 {
            color: #e53e3e;
            border-bottom-color: #f56565;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片压缩预览对比</h1>
        
        <div class="upload-section">
            <div id="uploadArea" class="upload-area">
                <p style="font-size: 18px; margin: 0;">📁 拖放图片到这里或点击选择文件</p>
                <p style="font-size: 14px; color: #666; margin: 10px 0 0 0;">支持 JPG、PNG、WebP 格式</p>
                <input type="file" id="fileInput" accept="image/*" style="display: none;">
            </div>
            
            <div>
                <button id="previewAllBtn" class="btn" disabled>🔍 预览所有压缩方法</button>
                <button id="clearBtn" class="btn secondary" disabled>🗑️ 清除</button>
            </div>
        </div>

        <div id="previewGrid" class="preview-grid hidden">
            <!-- 原始图片卡片 -->
            <div class="preview-card">
                <h3>📷 原始图片</h3>
                <img id="originalImage" class="preview-image">
                <div id="originalInfo" class="image-info"></div>
            </div>

            <!-- TinyPNG压缩卡片 -->
            <div id="tinypngCard" class="preview-card">
                <h3>🔧 TinyPNG 压缩</h3>
                <img id="tinypngImage" class="preview-image">
                <div id="tinypngInfo" class="image-info"></div>
                <div id="tinypngStats" class="compression-stats hidden"></div>
                <button id="tinypngDownload" class="btn download-btn hidden">💾 下载</button>
                <div id="tinypngLoading" class="loading-overlay hidden">
                    <div class="spinner"></div>
                </div>
            </div>

            <!-- iLoveAPI压缩卡片 -->
            <div id="iloveaviCard" class="preview-card">
                <h3>🚀 iLoveAPI 压缩</h3>
                <img id="iloveaviImage" class="preview-image">
                <div id="iloveaviInfo" class="image-info"></div>
                <div id="iloveaviStats" class="compression-stats hidden"></div>
                <button id="iloveaviDownload" class="btn download-btn hidden">💾 下载</button>
                <div id="iloveaviLoading" class="loading-overlay hidden">
                    <div class="spinner"></div>
                </div>
            </div>

            <!-- ImageUtil压缩卡片 -->
            <div id="imageutilCard" class="preview-card">
                <h3>⚡ ImageUtil 压缩</h3>
                <img id="imageutilImage" class="preview-image">
                <div id="imageutilInfo" class="image-info"></div>
                <div id="imageutilStats" class="compression-stats hidden"></div>
                <button id="imageutilDownload" class="btn download-btn hidden">💾 下载</button>
                <div id="imageutilLoading" class="loading-overlay hidden">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>

        <div id="statsSummary" class="stats-summary hidden">
            <h3>📊 压缩效果总结</h3>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const previewAllBtn = document.getElementById('previewAllBtn');
            const clearBtn = document.getElementById('clearBtn');
            const previewGrid = document.getElementById('previewGrid');
            const statsSummary = document.getElementById('statsSummary');
            const originalImage = document.getElementById('originalImage');
            const originalInfo = document.getElementById('originalInfo');

            let selectedFile = null;
            let originalSize = 0;
            let compressionResults = {};

            // 压缩方法配置 - 注意ID必须与HTML中的元素ID匹配
            const compressionMethods = [
                { id: 'tinypng', name: 'TinyPNG', endpoint: '/api/image/preview/tinypng' },
                { id: 'iloveavi', name: 'iLoveAPI', endpoint: '/api/image/preview/iloveapi' },
                { id: 'imageutil', name: 'ImageUtil', endpoint: '/api/image/preview/imageutil' }
            ];

            // 上传区域事件
            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', (e) => handleFiles(e.target.files));

            // 拖放功能
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('active');
            });
            uploadArea.addEventListener('dragleave', () => uploadArea.classList.remove('active'));
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('active');
                handleFiles(e.dataTransfer.files);
            });

            // 按钮事件
            previewAllBtn.addEventListener('click', previewAllMethods);
            clearBtn.addEventListener('click', clearAll);

            // 处理文件选择
            function handleFiles(files) {
                if (files.length === 0) return;

                selectedFile = files[0];
                if (!selectedFile.type.match('image.*')) {
                    alert('请选择图片文件！');
                    return;
                }

                // 显示原始图片
                const reader = new FileReader();
                reader.onload = function(e) {
                    originalImage.src = e.target.result;
                    previewGrid.classList.remove('hidden');
                    previewAllBtn.disabled = false;
                    clearBtn.disabled = false;

                    // 获取原始图片信息
                    getImageInfo(e.target.result, originalInfo, (info) => {
                        originalSize = info.size;
                    });
                };
                reader.readAsDataURL(selectedFile);
            }

            // 预览所有压缩方法
            async function previewAllMethods() {
                if (!selectedFile) return;

                previewAllBtn.disabled = true;
                compressionResults = {};

                // 并行处理所有压缩方法
                const promises = compressionMethods.map(method => compressWithMethod(method));
                await Promise.all(promises);

                // 显示总结
                showSummary();
                previewAllBtn.disabled = false;
            }

            // 使用指定方法压缩图片
            async function compressWithMethod(method) {
                const loadingElement = document.getElementById(`${method.id}Loading`);
                const imageElement = document.getElementById(`${method.id}Image`);
                const infoElement = document.getElementById(`${method.id}Info`);
                const statsElement = document.getElementById(`${method.id}Stats`);
                const downloadElement = document.getElementById(`${method.id}Download`);
                const cardElement = document.getElementById(`${method.id}Card`);

                console.log(`开始处理 ${method.name}, 查找元素ID: ${method.id}`);
                console.log(`找到的元素:`, {
                    loading: loadingElement,
                    image: imageElement,
                    info: infoElement,
                    stats: statsElement,
                    download: downloadElement,
                    card: cardElement
                });

                if (!loadingElement || !imageElement || !infoElement) {
                    console.error(`${method.name} 缺少必要的DOM元素`);
                    return;
                }

                try {
                    // 显示加载状态
                    loadingElement.classList.remove('hidden');
                    cardElement.classList.remove('error-card', 'best-compression');

                    console.log(`开始压缩: ${method.name}, 端点: ${method.endpoint}`);

                    const formData = new FormData();
                    formData.append('file', selectedFile);

                    const response = await fetch(method.endpoint, {
                        method: 'POST',
                        body: formData
                    });

                    console.log(`${method.name} 响应状态:`, response.status);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const blob = await response.blob();
                    const compressedUrl = URL.createObjectURL(blob);

                    console.log(`${method.name} 压缩完成, 大小:`, blob.size);

                    // 显示压缩后的图片
                    imageElement.src = compressedUrl;

                    // 获取压缩后图片信息
                    getImageInfo(compressedUrl, infoElement, (info) => {
                        const compressionRatio = ((originalSize - info.size) / originalSize * 100).toFixed(2);
                        const result = {
                            method: method.name,
                            originalSize: originalSize,
                            compressedSize: info.size,
                            compressionRatio: parseFloat(compressionRatio),
                            blob: blob,
                            url: compressedUrl
                        };

                        compressionResults[method.id] = result;

                        // 显示压缩统计
                        if (statsElement) {
                            showCompressionStats(statsElement, result);
                        }

                        // 设置下载功能
                        if (downloadElement) {
                            setupDownload(downloadElement, blob, `${method.id}_compressed_${selectedFile.name}`);
                        }
                    });

                } catch (error) {
                    console.error(`${method.name} compression failed:`, error);

                    // 显示错误状态
                    if (cardElement) {
                        cardElement.classList.add('error-card');
                    }
                    infoElement.innerHTML = `<p style="color: #e53e3e;">❌ 压缩失败: ${error.message}</p>`;

                    compressionResults[method.id] = {
                        method: method.name,
                        error: error.message,
                        failed: true
                    };
                } finally {
                    loadingElement.classList.add('hidden');
                }
            }

            // 显示压缩统计信息
            function showCompressionStats(element, result) {
                const { originalSize, compressedSize, compressionRatio } = result;
                const originalKB = (originalSize / 1024).toFixed(2);
                const compressedKB = (compressedSize / 1024).toFixed(2);

                let statusColor = '#666';
                let statusText = '';

                if (compressionRatio > 0) {
                    statusColor = '#28a745';
                    statusText = `节省 ${compressionRatio}%`;
                } else if (compressionRatio < 0) {
                    statusColor = '#dc3545';
                    statusText = `增大 ${Math.abs(compressionRatio)}%`;
                } else {
                    statusColor = '#6c757d';
                    statusText = '无变化';
                }

                element.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${originalKB} KB → ${compressedKB} KB</span>
                        <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                    </div>
                `;
                element.classList.remove('hidden');
            }

            // 设置下载功能
            function setupDownload(button, blob, filename) {
                button.onclick = () => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                };
                button.classList.remove('hidden');
            }

            // 显示压缩效果总结
            function showSummary() {
                const summaryContent = document.getElementById('summaryContent');
                const successfulResults = Object.values(compressionResults).filter(r => !r.failed);

                if (successfulResults.length === 0) {
                    summaryContent.innerHTML = '<p>❌ 所有压缩方法都失败了</p>';
                    statsSummary.classList.remove('hidden');
                    return;
                }

                // 找出最佳压缩方法
                const bestResult = successfulResults.reduce((best, current) =>
                    current.compressionRatio > best.compressionRatio ? current : best
                );

                // 高亮最佳压缩方法
                compressionMethods.forEach(method => {
                    const card = document.getElementById(`${method.id}Card`);
                    if (compressionResults[method.id] === bestResult) {
                        card.classList.add('best-compression');
                    }
                });

                // 生成总结内容
                let summaryHTML = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';

                successfulResults.forEach(result => {
                    const isBest = result === bestResult;
                    const badge = isBest ? '🏆 最佳' : '';

                    summaryHTML += `
                        <div style="padding: 10px; border: 1px solid ${isBest ? '#28a745' : '#ddd'}; border-radius: 4px; background-color: ${isBest ? '#f8fff8' : 'white'};">
                            <h4 style="margin: 0 0 8px 0; color: ${isBest ? '#28a745' : '#333'};">${result.method} ${badge}</h4>
                            <p style="margin: 4px 0; font-size: 12px;">压缩率: ${result.compressionRatio}%</p>
                            <p style="margin: 4px 0; font-size: 12px;">大小: ${(result.compressedSize / 1024).toFixed(2)} KB</p>
                        </div>
                    `;
                });

                summaryHTML += '</div>';

                // 添加整体统计
                const originalKB = (originalSize / 1024).toFixed(2);
                summaryHTML += `
                    <div style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                        <p style="margin: 0; font-weight: bold;">📈 原始图片: ${originalKB} KB</p>
                        <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                            最佳压缩可节省 ${bestResult.compressionRatio}% 空间
                        </p>
                    </div>
                `;

                summaryContent.innerHTML = summaryHTML;
                statsSummary.classList.remove('hidden');
            }

            // 获取图片信息
            function getImageInfo(src, infoElement, callback) {
                const img = new Image();
                img.onload = function() {
                    const width = this.width;
                    const height = this.height;

                    if (src.startsWith('blob:')) {
                        fetch(src)
                            .then(response => response.blob())
                            .then(blob => {
                                updateImageInfo(infoElement, width, height, blob.size);
                                if (callback) callback({ width, height, size: blob.size });
                            });
                    } else {
                        const base64 = src.split(',')[1];
                        const binarySize = atob(base64).length;
                        updateImageInfo(infoElement, width, height, binarySize);
                        if (callback) callback({ width, height, size: binarySize });
                    }
                };
                img.src = src;
            }

            // 更新图片信息显示
            function updateImageInfo(element, width, height, size) {
                const sizeKB = (size / 1024).toFixed(2);
                element.innerHTML = `
                    <p>📐 尺寸: ${width} × ${height} 像素</p>
                    <p>📦 大小: ${sizeKB} KB</p>
                `;
            }

            // 清除所有内容
            function clearAll() {
                selectedFile = null;
                originalSize = 0;
                compressionResults = {};

                previewGrid.classList.add('hidden');
                statsSummary.classList.add('hidden');
                previewAllBtn.disabled = true;
                clearBtn.disabled = true;

                // 清除所有图片和信息
                document.querySelectorAll('.preview-image').forEach(img => img.src = '');
                document.querySelectorAll('.image-info').forEach(info => info.innerHTML = '');
                document.querySelectorAll('.compression-stats').forEach(stats => {
                    stats.classList.add('hidden');
                    stats.innerHTML = '';
                });
                document.querySelectorAll('.download-btn').forEach(btn => btn.classList.add('hidden'));
                document.querySelectorAll('.preview-card').forEach(card => {
                    card.classList.remove('error-card', 'best-compression');
                });

                // 重置文件输入
                fileInput.value = '';
            }
        });
    </script>
</body>
</html>
