<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FLV直播播放器</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 20px;
        }
        .input-group {
            display: flex;
            margin-bottom: 20px;
        }
        .input-group input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 16px;
        }
        .input-group button {
            padding: 10px 15px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .input-group button:hover {
            background-color: #0b7dda;
        }
        .video-container {
            width: 100%;
            margin-bottom: 20px;
            background-color: #000;
            border-radius: 4px;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: 450px;
            background-color: #000;
        }
        .controls {
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
        .controls button {
            margin: 0 5px;
            padding: 8px 15px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #0b7dda;
        }
        .error {
            color: #f44336;
            margin-top: 10px;
            text-align: center;
            display: none;
        }
        .sample-urls {
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
        }
        .sample-urls h3 {
            margin-top: 0;
            color: #555;
        }
        .sample-urls ul {
            padding-left: 20px;
        }
        .sample-urls li {
            margin-bottom: 5px;
        }
        .sample-urls button {
            background: none;
            border: none;
            color: #2196F3;
            cursor: pointer;
            padding: 0;
            font-size: inherit;
            text-decoration: underline;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px 20px;
            border-radius: 4px;
            display: none;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            display: none;
        }
        .note {
            margin-top: 20px;
            padding: 10px;
            background-color: #e8f4fd;
            border-left: 4px solid #2196F3;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FLV直播播放器</h1>
        
        <div class="input-group">
            <input type="text" id="flv-url" placeholder="请输入HTTP-FLV直播地址 (例如: http://example.com/live/stream.flv)" />
            <button id="play-btn">播放</button>
        </div>
        
        <div id="error-message" class="error">无法播放此直播流，请检查URL是否正确</div>
        
        <div class="video-container">
            <video id="videoElement" controls></video>
        </div>
        
        <div class="controls">
            <button id="play-pause-btn">暂停</button>
            <button id="mute-btn">静音</button>
            <button id="fullscreen-btn">全屏</button>
            <button id="reload-btn">重新加载</button>
        </div>
        
        <div id="status" class="status"></div>
        
        <div class="sample-urls">
            <h3>示例HTTP-FLV地址</h3>
            <ul>
                <li><button class="sample-url-btn" data-url="https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv">示例FLV流 1</button></li>
                <li><button class="sample-url-btn" data-url="https://flv-sample.oss-cn-shanghai.aliyuncs.com/sample.flv">示例FLV流 2</button></li>
            </ul>
            <p><small>注意：这些是公开的测试流，可能随时失效</small></p>
        </div>
        
        <div class="note">
            <p>FLV播放器使用说明：</p>
            <ol>
                <li>输入HTTP-FLV直播地址（格式通常为 http://server/path/stream.flv）</li>
                <li>点击"播放"按钮开始播放</li>
                <li>使用下方控制按钮控制播放</li>
                <li>如果播放失败，请检查：
                    <ul>
                        <li>FLV地址是否正确</li>
                        <li>您的浏览器是否支持MSE（Media Source Extensions）</li>
                        <li>是否有跨域访问限制（CORS）</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <!-- 引入flv.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/flv.js@1.6.2/dist/flv.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查浏览器是否支持flv.js
            if (flvjs.isSupported()) {
                // 获取DOM元素
                const videoElement = document.getElementById('videoElement');
                const urlInput = document.getElementById('flv-url');
                const playBtn = document.getElementById('play-btn');
                const errorMessage = document.getElementById('error-message');
                const status = document.getElementById('status');
                const sampleUrlBtns = document.querySelectorAll('.sample-url-btn');
                const playPauseBtn = document.getElementById('play-pause-btn');
                const muteBtn = document.getElementById('mute-btn');
                const fullscreenBtn = document.getElementById('fullscreen-btn');
                const reloadBtn = document.getElementById('reload-btn');
                
                // flv.js播放器实例
                let flvPlayer = null;
                
                // 播放按钮点击事件
                playBtn.addEventListener('click', function() {
                    playStream(urlInput.value.trim());
                });
                
                // 输入框回车事件
                urlInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        playStream(urlInput.value.trim());
                    }
                });
                
                // 示例URL按钮点击事件
                sampleUrlBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const url = this.getAttribute('data-url');
                        urlInput.value = url;
                        playStream(url);
                    });
                });
                
                // 播放/暂停按钮点击事件
                playPauseBtn.addEventListener('click', function() {
                    if (videoElement.paused) {
                        videoElement.play();
                        playPauseBtn.textContent = '暂停';
                    } else {
                        videoElement.pause();
                        playPauseBtn.textContent = '播放';
                    }
                });
                
                // 静音按钮点击事件
                muteBtn.addEventListener('click', function() {
                    videoElement.muted = !videoElement.muted;
                    muteBtn.textContent = videoElement.muted ? '取消静音' : '静音';
                });
                
                // 全屏按钮点击事件
                fullscreenBtn.addEventListener('click', function() {
                    if (videoElement.requestFullscreen) {
                        videoElement.requestFullscreen();
                    } else if (videoElement.webkitRequestFullscreen) { /* Safari */
                        videoElement.webkitRequestFullscreen();
                    } else if (videoElement.msRequestFullscreen) { /* IE11 */
                        videoElement.msRequestFullscreen();
                    }
                });
                
                // 重新加载按钮点击事件
                reloadBtn.addEventListener('click', function() {
                    if (flvPlayer) {
                        const currentUrl = flvPlayer.config.url;
                        destroyPlayer();
                        playStream(currentUrl);
                    }
                });
                
                // 播放流函数
                function playStream(url) {
                    if (!url) {
                        showError('请输入HTTP-FLV直播地址');
                        return;
                    }
                    
                    // 隐藏错误消息
                    hideError();
                    
                    // 销毁之前的播放器实例
                    destroyPlayer();
                    
                    try {
                        // 创建flv.js播放器
                        flvPlayer = flvjs.createPlayer({
                            type: 'flv',
                            url: url,
                            isLive: true,
                            cors: true,
                            hasAudio: true,
                            hasVideo: true,
                            enableStashBuffer: false
                        });
                        
                        // 绑定到视频元素
                        flvPlayer.attachMediaElement(videoElement);
                        
                        // 加载并播放
                        flvPlayer.load();
                        flvPlayer.play();
                        
                        // 显示状态信息
                        showStatus('正在加载FLV流...');
                        
                        // 监听事件
                        flvPlayer.on(flvjs.Events.LOADING_COMPLETE, function() {
                            showStatus('FLV流加载完成');
                        });
                        
                        flvPlayer.on(flvjs.Events.ERROR, function(errorType, errorDetail) {
                            console.error('FLV播放错误:', errorType, errorDetail);
                            showError('播放出错: ' + errorType);
                        });
                        
                        // 视频元素事件
                        videoElement.addEventListener('playing', function() {
                            showStatus('FLV流正在播放');
                            playPauseBtn.textContent = '暂停';
                        });
                        
                        videoElement.addEventListener('waiting', function() {
                            showStatus('FLV流缓冲中...');
                        });
                        
                        videoElement.addEventListener('error', function() {
                            showError('视频播放出错');
                        });
                        
                    } catch (error) {
                        console.error('创建FLV播放器时出错:', error);
                        showError('无法播放此直播流，请检查URL是否正确');
                    }
                }
                
                // 销毁播放器实例
                function destroyPlayer() {
                    if (flvPlayer) {
                        flvPlayer.pause();
                        flvPlayer.unload();
                        flvPlayer.detachMediaElement();
                        flvPlayer.destroy();
                        flvPlayer = null;
                    }
                }
                
                // 显示错误消息
                function showError(message) {
                    errorMessage.textContent = message;
                    errorMessage.style.display = 'block';
                    status.style.display = 'none';
                }
                
                // 隐藏错误消息
                function hideError() {
                    errorMessage.style.display = 'none';
                }
                
                // 显示状态信息
                function showStatus(message) {
                    status.textContent = message;
                    status.style.display = 'block';
                }
                
            } else {
                // 浏览器不支持flv.js
                document.getElementById('error-message').textContent = '您的浏览器不支持FLV播放，请使用Chrome、Firefox或Edge的最新版本';
                document.getElementById('error-message').style.display = 'block';
            }
        });
    </script>
</body>
</html>
