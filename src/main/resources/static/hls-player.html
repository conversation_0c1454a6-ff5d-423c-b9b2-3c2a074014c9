<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS直播播放器</title>
    <!-- 引入Video.js CSS -->
    <link href="https://vjs.zencdn.net/7.20.3/video-js.css" rel="stylesheet" />
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #2196F3;
            margin-bottom: 20px;
        }
        .input-group {
            display: flex;
            margin-bottom: 20px;
        }
        .input-group input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 16px;
        }
        .input-group button {
            padding: 10px 15px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .input-group button:hover {
            background-color: #0b7dda;
        }
        .video-container {
            width: 100%;
            margin-bottom: 20px;
            background-color: #000;
            border-radius: 4px;
            overflow: hidden;
        }
        .video-js {
            width: 100%;
            height: 450px;
        }
        .history {
            margin-top: 20px;
        }
        .history h3 {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            color: #555;
        }
        .history-list {
            list-style: none;
            padding: 0;
        }
        .history-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            justify-content: space-between;
        }
        .history-list button {
            background-color: transparent;
            border: none;
            color: #2196F3;
            cursor: pointer;
        }
        .history-list button:hover {
            text-decoration: underline;
        }
        .error {
            color: #f44336;
            margin-top: 10px;
            text-align: center;
            display: none;
        }
        .sample-urls {
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
        }
        .sample-urls h3 {
            margin-top: 0;
            color: #555;
        }
        .sample-urls ul {
            padding-left: 20px;
        }
        .sample-urls li {
            margin-bottom: 5px;
        }
        .sample-urls button {
            background: none;
            border: none;
            color: #2196F3;
            cursor: pointer;
            padding: 0;
            font-size: inherit;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HLS直播播放器</h1>
        
        <div class="input-group">
            <input type="text" id="hls-url" placeholder="请输入HLS直播地址 (例如: https://example.com/live/stream.m3u8)" />
            <button id="play-btn">播放</button>
        </div>
        
        <div id="error-message" class="error">无法播放此直播流，请检查URL是否正确</div>
        
        <div class="video-container">
            <video id="hls-player" class="video-js vjs-default-skin vjs-big-play-centered" controls preload="auto">
                <p class="vjs-no-js">
                    要查看此视频，请启用JavaScript，并考虑升级到支持HTML5视频的浏览器
                </p>
            </video>
        </div>
        
        <div class="sample-urls">
            <h3>示例HLS地址</h3>
            <ul>
                <li><button class="sample-url-btn" data-url="https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8">测试流 1 (Mux测试流)</button></li>
                <li><button class="sample-url-btn" data-url="https://cph-p2p-msl.akamaized.net/hls/live/2000341/test/master.m3u8">测试流 2 (Akamai测试流)</button></li>
                <li><button class="sample-url-btn" data-url="https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8">测试流 3 (Unified Streaming测试流)</button></li>
            </ul>
            <p><small>注意：这些是公开的测试流，可能随时失效</small></p>
        </div>
        
        <div class="history">
            <h3>播放历史</h3>
            <ul id="history-list" class="history-list">
                <!-- 历史记录将在这里动态添加 -->
            </ul>
        </div>
    </div>

    <!-- 引入Video.js -->
    <script src="https://vjs.zencdn.net/7.20.3/video.min.js"></script>
    <!-- 引入Video.js的HLS插件 -->
    <script src="https://cdn.jsdelivr.net/npm/videojs-contrib-hls@5.15.0/dist/videojs-contrib-hls.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化播放器
            const player = videojs('hls-player', {
                fluid: true,
                html5: {
                    hls: {
                        overrideNative: true
                    }
                }
            });
            
            // 获取DOM元素
            const urlInput = document.getElementById('hls-url');
            const playBtn = document.getElementById('play-btn');
            const errorMessage = document.getElementById('error-message');
            const historyList = document.getElementById('history-list');
            const sampleUrlBtns = document.querySelectorAll('.sample-url-btn');
            
            // 从本地存储加载历史记录
            loadHistory();
            
            // 播放按钮点击事件
            playBtn.addEventListener('click', function() {
                playStream(urlInput.value.trim());
            });
            
            // 输入框回车事件
            urlInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    playStream(urlInput.value.trim());
                }
            });
            
            // 示例URL按钮点击事件
            sampleUrlBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const url = this.getAttribute('data-url');
                    urlInput.value = url;
                    playStream(url);
                });
            });
            
            // 播放流函数
            function playStream(url) {
                if (!url) {
                    showError('请输入HLS直播地址');
                    return;
                }
                
                // 隐藏错误消息
                hideError();
                
                try {
                    // 设置新的视频源
                    player.src({
                        src: url,
                        type: 'application/x-mpegURL'
                    });
                    
                    // 加载并播放
                    player.load();
                    player.play().catch(e => {
                        console.error('播放失败:', e);
                        // 某些浏览器策略要求用户交互后才能自动播放
                        // 这里我们不显示错误，因为用户可以点击播放按钮
                    });
                    
                    // 添加到历史记录
                    addToHistory(url);
                    
                } catch (error) {
                    console.error('设置视频源时出错:', error);
                    showError('无法播放此直播流，请检查URL是否正确');
                }
            }
            
            // 显示错误消息
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
            }
            
            // 隐藏错误消息
            function hideError() {
                errorMessage.style.display = 'none';
            }
            
            // 添加到历史记录
            function addToHistory(url) {
                // 从本地存储获取历史记录
                let history = JSON.parse(localStorage.getItem('hlsHistory')) || [];
                
                // 如果URL已存在，则移除旧记录
                history = history.filter(item => item !== url);
                
                // 添加到历史记录开头
                history.unshift(url);
                
                // 限制历史记录数量为10条
                if (history.length > 10) {
                    history = history.slice(0, 10);
                }
                
                // 保存到本地存储
                localStorage.setItem('hlsHistory', JSON.stringify(history));
                
                // 更新UI
                updateHistoryUI();
            }
            
            // 加载历史记录
            function loadHistory() {
                updateHistoryUI();
            }
            
            // 更新历史记录UI
            function updateHistoryUI() {
                // 清空历史记录列表
                historyList.innerHTML = '';
                
                // 从本地存储获取历史记录
                const history = JSON.parse(localStorage.getItem('hlsHistory')) || [];
                
                // 如果没有历史记录
                if (history.length === 0) {
                    const li = document.createElement('li');
                    li.textContent = '暂无播放历史';
                    historyList.appendChild(li);
                    return;
                }
                
                // 添加历史记录到列表
                history.forEach(url => {
                    const li = document.createElement('li');
                    
                    // 创建URL显示元素
                    const urlSpan = document.createElement('span');
                    // 截断过长的URL
                    urlSpan.textContent = url.length > 50 ? url.substring(0, 47) + '...' : url;
                    urlSpan.title = url; // 鼠标悬停时显示完整URL
                    li.appendChild(urlSpan);
                    
                    // 创建按钮容器
                    const btnContainer = document.createElement('div');
                    
                    // 创建播放按钮
                    const playBtn = document.createElement('button');
                    playBtn.textContent = '播放';
                    playBtn.addEventListener('click', function() {
                        urlInput.value = url;
                        playStream(url);
                    });
                    btnContainer.appendChild(playBtn);
                    
                    // 添加按钮容器到列表项
                    li.appendChild(btnContainer);
                    
                    // 添加列表项到历史记录列表
                    historyList.appendChild(li);
                });
            }
            
            // 错误处理
            player.on('error', function() {
                showError('播放出错，请检查URL是否正确或网络连接是否正常');
            });
        });
    </script>
</body>
</html>
