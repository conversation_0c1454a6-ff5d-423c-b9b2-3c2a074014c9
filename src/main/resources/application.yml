server:
  port: 8090

spring:
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
  datasource:
    url: jdbc:mysql://***********:8066/compression?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&autoReconnect=true
    username: hudong_test@hudong_mysql_test
    password: 2cJKDaxSsaUaYAuqcoEd1VP4
    driver-class-name: com.mysql.cj.jdbc.Driver

# MyBatis配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-aliases-package: com.yy.hd.entity

# TinyPNG配置
tinypng:
  # API密钥列表，支持多个key轮流使用
  api-keys:
    - SN1z6JK0q0vr38zpglFHVQFJf6qdCmTy # lvj
    - 7k2P9ZkNsT4wr9khnfLHHZmq2Jtwt9RT # wzl
    - jvG4ft20vDCfVbGg8pdhhFClShjJlMl2 # lwz
    # 可以添加更多key，例如：
    # - your_second_api_key_here
    # - your_third_api_key_here
  # 每个key的月使用限制（默认500次）
  monthly-limit: 500
  # 是否启用key轮换
  rotation-enabled: true

# iLoveAPI配置
iloveapi:
  # 公钥列表，支持多个key轮流使用
  public-keys:
    - project_public_5731b701d86558171e75348505ff9553_hxrlO8467c7224ac71aca9c44cc017e6dacfa
    # 可以添加更多公钥，例如：
    # - your_second_public_key_here
    # - your_third_public_key_here
  # 每个key的月使用限制（默认500次）
  monthly-limit: 500
  # 是否启用key轮换
  rotation-enabled: true
  # API基础URL
  base-url: https://api.ilovepdf.com/v1
  # 连接超时时间（秒）
  connect-timeout: 30
  # 读取超时时间（秒）
  read-timeout: 120
  # 是否启用
  enabled: true

# 图片缓存配置
image:
  cache:
    # 缓存目录路径
    directory: ./cache/images
    # 缓存过期时间（天）
    expire-days: 30
    # 是否启用缓存
    enabled: true