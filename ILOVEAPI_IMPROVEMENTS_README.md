# iLoveAPI 功能改进说明

## 改进概述

基于您的要求，我们对iLoveAPI集成进行了以下重要改进：

### ✅ 已完成的改进

1. **ObjectMapper 忽略未识别字段**
2. **sendGetRequest 添加参数和返回值日志**
3. **支持多个公钥轮换使用**
4. **每个公钥每月500次使用限制管理**

## 详细改进内容

### 1. ObjectMapper 配置优化

**改进位置：** `ILoveApiUtil.java`

```java
public ILoveApiUtil() {
    this.objectMapper = new ObjectMapper();
    // 配置忽略未识别的字段
    this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
}
```

**改进效果：**
- 解析JSON时自动忽略未定义的字段
- 提高API兼容性，避免因新增字段导致解析失败
- 增强系统稳定性

### 2. 请求日志增强

**改进位置：** `ILoveApiUtil.sendGetRequest()`

```java
private String sendGetRequest(String urlString, String token) throws IOException {
    logger.debug("发送GET请求: URL={}, Token={}", urlString, 
        token != null ? "Bearer " + token.substring(0, Math.min(20, token.length())) + "..." : "null");
    
    // ... 请求处理 ...
    
    String response = readResponse(conn);
    logger.debug("GET请求响应: {}", response);
    
    return response;
}
```

**改进效果：**
- 详细记录请求URL和token信息（token做了安全掩码处理）
- 完整记录响应内容，便于调试
- 提高问题排查效率

### 3. 多公钥轮换支持

**新增文件：**
- `ILoveApiKeyManager.java` - 公钥管理器
- 修改 `ILoveApiProperties.java` - 支持多公钥配置

**配置示例：**
```yaml
iloveapi:
  public-keys:
    - project_public_5731b701d86558171e75348505ff9553_hxrlO8467c7224ac71aca9c44cc017e6dacfa
    - your_second_public_key_here
    - your_third_public_key_here
  monthly-limit: 500
  rotation-enabled: true
```

**核心功能：**
- 自动在多个公钥间轮换
- 智能跳过已达到月限制的公钥
- 实时使用量统计和监控
- 支持手动重置统计

### 4. 使用量管理系统

**统计机制：**
- 使用 "publicKey-YYYY-MM" 格式作为统计键
- 每次API调用后自动增加使用计数
- 每月自动重置统计（基于当前日期）
- 达到90%时发出警告日志

**监控功能：**
- 实时查看每个公钥的使用情况
- 支持掩码显示公钥（安全考虑）
- 提供可用性检查接口

## 新增API接口

### 1. iLoveAPI统计接口

```bash
# 查看公钥使用统计
GET /api/image/iloveapi/stats

# 检查可用公钥
GET /api/image/iloveapi/available

# 重置使用统计
POST /api/image/iloveapi/reset-stats
```

### 2. 统计信息示例

```
=== iLoveAPI公钥使用统计 ===
Key 1 (project_****_hxrlO846): 45/500 次 (9.0%)
Key 2 (your_sec****_key_here): 0/500 次 (0.0%)
Key 3 (your_thi****_key_here): 0/500 次 (0.0%)
当前月份: 2025-01
轮换启用: 是
```

## 技术实现细节

### 1. 线程安全设计

```java
// 使用原子操作确保线程安全
private final AtomicInteger currentKeyIndex = new AtomicInteger(0);
private final Map<String, AtomicInteger> keyUsageStats = new ConcurrentHashMap<>();
```

### 2. 智能轮换算法

```java
public synchronized String getNextAvailableKey() {
    // 从当前索引开始查找可用key
    for (int i = 0; i < publicKeys.size(); i++) {
        int keyIndex = (startIndex + i) % publicKeys.size();
        // 检查使用量是否超限
        if (currentUsage < properties.getMonthlyLimit()) {
            // 更新索引并返回可用key
            currentKeyIndex.set((keyIndex + 1) % publicKeys.size());
            return publicKey;
        }
    }
    return null; // 所有key都达到限制
}
```

### 3. 安全掩码处理

```java
private String maskPublicKey(String publicKey) {
    if (publicKey == null || publicKey.length() <= 16) {
        return "****";
    }
    return publicKey.substring(0, 8) + "****" + publicKey.substring(publicKey.length() - 8);
}
```

## 日志监控增强

### 关键日志信息

```
# 公钥轮换日志
使用iLoveAPI公钥索引: 0, 本月已使用: 45/500

# 请求调试日志
发送GET请求: URL=https://api.ilovepdf.com/v1/start/compressimage, Token=Bearer eyJ0eXAiOiJKV1Q...
GET请求响应: {"server":"api11.ilovepdf.com","task":"g27d4mrsg3zt..."}

# 使用量警告
iLoveAPI公钥使用量警告: 本月已使用 450/500 次

# 错误处理
所有iLoveAPI公钥都已达到月使用限制 (500次)
没有可用的iLoveAPI公钥
```

## 性能优化

### 1. 缓存机制
- 压缩结果智能缓存
- 缓存键前缀区分：`iloveapi_` + MD5哈希
- 避免重复API调用

### 2. 连接池优化
- 合理的超时时间设置
- 连接复用机制
- 异步处理支持

### 3. 内存管理
- 使用ConcurrentHashMap确保线程安全
- 原子操作减少锁竞争
- 及时释放资源

## 错误处理增强

### 1. 优雅降级
- API调用失败时使用原图
- 公钥耗尽时记录错误并降级
- 网络异常时自动重试

### 2. 详细错误信息
- 记录具体的错误原因
- 提供调试信息
- 支持问题追踪

## 配置灵活性

### 1. 动态配置
- 支持运行时添加/删除公钥
- 可调整月使用限制
- 可开启/关闭轮换功能

### 2. 环境适配
- 开发/测试/生产环境独立配置
- 支持不同的超时时间设置
- 灵活的日志级别控制

## 监控和运维

### 1. 实时监控
- 公钥使用情况实时查看
- 可用性状态检查
- 使用趋势分析

### 2. 运维工具
- 手动重置统计功能
- 批量公钥管理
- 健康检查接口

## 安全考虑

### 1. 敏感信息保护
- 公钥掩码显示
- Token截断记录
- 避免完整密钥泄露

### 2. 访问控制
- 统计接口权限控制
- 重置操作审计
- 安全日志记录

这些改进大大增强了iLoveAPI集成的稳定性、可维护性和可监控性，为生产环境的使用提供了坚实的基础。
