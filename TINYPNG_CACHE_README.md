# TinyPNG 缓存功能说明

## 功能概述

为了减少TinyPNG API的调用量和提高响应速度，我们为`TinyPngUtil.compress`方法添加了磁盘缓存功能。

## 主要特性

### 1. 智能缓存
- 基于图片内容的MD5哈希生成缓存键，确保相同内容的图片使用相同缓存
- 自动检测缓存有效性，避免使用过期缓存
- 只有压缩后文件更小的图片才会被缓存

### 2. 配置灵活
- 可通过`application.yml`配置缓存目录、过期时间等参数
- 支持启用/禁用缓存功能
- 默认缓存30天，可根据需要调整

### 3. 自动管理
- 定时清理过期缓存文件（每天凌晨2点）
- 提供缓存统计和手动清理接口
- 自动创建缓存目录

## 配置说明

### application.yml 配置
```yaml
# 图片缓存配置
image:
  cache:
    # 缓存目录路径
    directory: ./cache/images
    # 缓存过期时间（天）
    expire-days: 30
    # 是否启用缓存
    enabled: true
```

### 配置参数说明
- `directory`: 缓存文件存储目录，默认为`./cache/images`
- `expire-days`: 缓存过期时间（天），默认30天
- `enabled`: 是否启用缓存功能，默认true

## API接口

### 1. 图片压缩（支持缓存）
```
POST /api/image/compress
```
- 上传图片文件进行压缩
- 自动检查缓存，存在则直接返回
- 不存在则调用TinyPNG API并缓存结果

### 2. 缓存统计信息
```
GET /api/image/cache/stats
```
- 返回缓存文件数量和总大小

### 3. 清理过期缓存
```
POST /api/image/cache/clean
```
- 手动清理过期的缓存文件

## 使用方式

### 1. 编程方式使用
```java
@Autowired
private TinyPngUtil tinyPngUtil;

// 使用支持缓存的压缩方法
tinyPngUtil.compress(inputStream, outputStream);
```

### 2. HTTP接口使用
```javascript
// 上传图片进行压缩
const formData = new FormData();
formData.append('file', imageFile);

fetch('/api/image/compress', {
    method: 'POST',
    body: formData
})
.then(response => response.blob())
.then(compressedImage => {
    // 处理压缩后的图片
});
```

## 缓存工作流程

1. **接收压缩请求**
   - 读取输入图片数据
   - 计算图片内容的MD5哈希值作为缓存键

2. **检查缓存**
   - 根据缓存键查找对应的缓存文件
   - 检查缓存文件是否存在且未过期

3. **缓存命中**
   - 直接读取缓存文件内容
   - 返回压缩后的图片数据
   - 记录缓存使用日志

4. **缓存未命中**
   - 调用TinyPNG API进行压缩
   - 检查压缩效果（只有文件变小才缓存）
   - 将压缩结果写入缓存文件
   - 返回压缩后的图片数据

## 性能优势

### 1. 响应速度提升
- 缓存命中时响应时间从秒级降低到毫秒级
- 避免网络请求延迟

### 2. API调用量减少
- 相同图片只需调用一次TinyPNG API
- 显著降低API使用成本

### 3. 服务稳定性
- 减少对外部API的依赖
- 提高服务可用性

## 监控和维护

### 1. 缓存统计
```bash
curl http://localhost:8090/api/image/cache/stats
```

### 2. 手动清理
```bash
curl -X POST http://localhost:8090/api/image/cache/clean
```

### 3. 日志监控
- 缓存命中/未命中日志
- 压缩效果日志
- 定时清理日志

## 注意事项

1. **磁盘空间**: 确保缓存目录有足够的磁盘空间
2. **权限设置**: 确保应用有读写缓存目录的权限
3. **备份策略**: 缓存文件可以安全删除，会自动重新生成
4. **网络环境**: 首次压缩仍需要网络访问TinyPNG API

## 故障排除

### 缓存不生效
1. 检查`image.cache.enabled`配置是否为true
2. 检查缓存目录权限
3. 查看应用日志中的缓存相关信息

### 磁盘空间不足
1. 手动清理过期缓存
2. 调整`expire-days`配置
3. 定期监控磁盘使用情况

### 压缩效果异常
1. 检查TinyPNG API密钥是否有效
2. 确认网络连接正常
3. 查看压缩日志中的错误信息
