# iLoveAPI 异步图片压缩集成说明

## 功能概述

我们已经成功集成了iLoveAPI的异步图片压缩服务。iLoveAPI是一个功能强大的在线文档和图片处理平台，提供高质量的图片压缩服务。

## 主要特性

### 1. 异步压缩流程
- 采用标准的异步处理流程：认证 → 开始任务 → 上传文件 → 处理任务 → 下载结果
- 支持多种图片格式：JPG、PNG、WebP等
- 提供多种压缩级别：extreme（极限）、recommended（推荐）、low（低压缩）

### 2. 智能缓存
- 集成现有的磁盘缓存系统
- 避免重复调用API，提高响应速度
- 只有压缩效果更好的图片才会被缓存

### 3. 优雅降级
- API调用失败时自动使用原图
- 压缩后文件更大时使用原图
- 服务未启用时直接传输原图

## 配置说明

### application.yml 配置
```yaml
# iLoveAPI配置
iloveapi:
  # 公钥列表，支持多个key轮流使用
  public-keys:
    - project_public_5731b701d86558171e75348505ff9553_hxrlO8467c7224ac71aca9c44cc017e6dacfa
    # 可以添加更多公钥，例如：
    # - your_second_public_key_here
    # - your_third_public_key_here
  # 每个key的月使用限制（默认500次）
  monthly-limit: 500
  # 是否启用key轮换
  rotation-enabled: true
  # API基础URL
  base-url: https://api.ilovepdf.com/v1
  # 连接超时时间（秒）
  connect-timeout: 30
  # 读取超时时间（秒）
  read-timeout: 120
  # 是否启用
  enabled: true
```

### 配置参数说明
- `public-keys`: iLoveAPI项目公钥列表，支持多个key轮换使用
- `monthly-limit`: 每个key的月使用限制，默认500次
- `rotation-enabled`: 是否启用key轮换，默认true
- `base-url`: API基础URL，默认为官方地址
- `connect-timeout`: 连接超时时间，默认30秒
- `read-timeout`: 读取超时时间，默认120秒（异步处理可能需要较长时间）
- `enabled`: 是否启用iLoveAPI压缩，默认true

## API接口

### 1. 图片压缩接口
```
POST /api/image/compress/iloveapi
```

**请求参数：**
- `file`: 要压缩的图片文件（multipart/form-data）

**支持的图片格式：**
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)

**响应：**
- 成功：返回压缩后的图片数据
- 失败：返回原图数据（优雅降级）

### 2. iLoveAPI统计接口

#### 获取公钥使用统计
```
GET /api/image/iloveapi/stats
```
返回示例：
```
=== iLoveAPI公钥使用统计 ===
Key 1 (project_****_hxrlO846): 45/500 次 (9.0%)
Key 2 (your_sec****_key_here): 0/500 次 (0.0%)
Key 3 (your_thi****_key_here): 0/500 次 (0.0%)
当前月份: 2025-01
轮换启用: 是
```

#### 检查可用公钥状态
```
GET /api/image/iloveapi/available
```
- 返回是否还有可用的iLoveAPI公钥

#### 重置使用统计
```
POST /api/image/iloveapi/reset-stats
```
- 重置当前月份的所有公钥使用统计

## 使用方式

### 1. 通过HTTP接口
```bash
# 压缩图片
curl -X POST -F "file=@image.jpg" http://localhost:8090/api/image/compress/iloveapi
```

### 2. 通过前端页面
```html
<form action="/api/image/compress/iloveapi" method="post" enctype="multipart/form-data">
    <input type="file" name="file" accept="image/*">
    <button type="submit">压缩图片</button>
</form>
```

### 3. JavaScript示例
```javascript
const formData = new FormData();
formData.append('file', imageFile);

fetch('/api/image/compress/iloveapi', {
    method: 'POST',
    body: formData
})
.then(response => response.blob())
.then(compressedImage => {
    // 处理压缩后的图片
    const url = URL.createObjectURL(compressedImage);
    document.getElementById('result').src = url;
});
```

## 工作原理

### 异步压缩流程
1. **认证 (/auth)**: 使用公钥获取JWT token
2. **开始任务 (/start)**: 创建压缩任务，获取服务器地址和任务ID
3. **上传文件 (/upload)**: 将图片文件上传到指定服务器
4. **处理任务 (/process)**: 启动压缩处理，等待完成
5. **下载结果 (/download)**: 下载压缩后的图片

### 缓存机制
- 使用图片内容的MD5哈希作为缓存键
- 缓存键前缀为 "iloveapi_" 以区分其他压缩方式
- 缓存有效期为30天（可配置）
- 支持自动清理过期缓存

### 错误处理
- 网络错误：自动重试或使用原图
- API限制：记录日志并使用原图
- 压缩失败：记录错误信息并使用原图
- 文件格式不支持：使用原图

## 性能特点

### 1. 压缩质量
- 采用先进的压缩算法
- 支持多种压缩级别选择
- 保持良好的图片质量

### 2. 处理速度
- 异步处理，避免阻塞
- 智能缓存，提高响应速度
- 支持并发处理多个请求

### 3. 资源消耗
- 服务端处理，不占用本地资源
- 支持大文件处理
- 自动内存管理

## 监控和日志

### 关键日志信息
```
# 开始压缩
开始iLoveAPI异步压缩流程: iloveapi_abc123...

# 各阶段成功
获取认证token成功
开始任务成功: server=api11.ilovepdf.com, task=g27d4mrsg3zt...
上传文件成功: cd29201ebe...5257c95d9843.jpg
处理任务成功: status=TaskSuccess
下载结果成功: 15234 字节

# 压缩结果
iLoveAPI压缩成功: 25678 -> 15234 字节

# 缓存使用
使用缓存的iLoveAPI压缩图片: iloveapi_abc123...

# 错误处理
iLoveAPI压缩失败，使用原图: 请求失败: HTTP 429 - Too many requests
```

## 与其他压缩方式对比

| 特性 | iLoveAPI | TinyPNG | ImageUtil |
|------|----------|---------|-----------|
| 压缩质量 | 高 | 高 | 中 |
| 处理速度 | 中（异步） | 快 | 快 |
| API限制 | 有 | 有（500次/月） | 无 |
| 支持格式 | 多种 | PNG/JPG | 多种 |
| 缓存支持 | 是 | 是 | 否 |
| 成本 | 按使用量 | 免费额度 | 免费 |

## 注意事项

1. **API密钥安全**: 不要在客户端代码中暴露公钥
2. **网络依赖**: 需要稳定的网络连接访问iLoveAPI服务
3. **处理时间**: 异步处理可能需要较长时间，建议设置合适的超时时间
4. **使用限制**: 注意API的使用限制和配额
5. **错误处理**: 确保有完善的错误处理和降级机制

## 故障排除

### 1. 认证失败
- 检查公钥是否正确
- 确认网络连接正常
- 查看API服务状态

### 2. 上传失败
- 检查文件大小限制
- 确认文件格式支持
- 查看网络连接状态

### 3. 处理超时
- 增加读取超时时间
- 检查文件大小是否过大
- 确认API服务响应正常

### 4. 下载失败
- 检查任务状态
- 确认处理已完成
- 查看错误日志信息

## 扩展建议

1. **批量处理**: 支持一次处理多个图片文件
2. **进度监控**: 添加处理进度查询接口
3. **统计分析**: 记录压缩效果和使用统计
4. **配置优化**: 支持动态调整压缩参数
5. **负载均衡**: 在多个API服务间分配请求
