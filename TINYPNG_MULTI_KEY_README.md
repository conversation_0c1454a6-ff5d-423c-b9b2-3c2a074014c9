# TinyPNG 多API Key轮换功能说明

## 功能概述

为了解决TinyPNG每个账号每月只能使用500次的限制，我们实现了多个API key轮换使用的功能。系统会自动在多个API key之间轮换，当某个key达到月使用限制时，自动切换到下一个可用的key。

## 主要特性

### 1. 多Key轮换
- 支持配置多个TinyPNG API key
- 自动在key之间轮换使用
- 智能跳过已达到月限制的key

### 2. 使用量统计
- 实时跟踪每个key的月使用量
- 自动按月重置使用统计
- 提供详细的使用统计报告

### 3. 智能管理
- 当使用量达到90%时自动警告
- 所有key都达到限制时使用原图
- 支持手动重置使用统计

## 配置说明

### application.yml 配置
```yaml
# TinyPNG配置
tinypng:
  # API密钥列表，支持多个key轮流使用
  api-keys:
    - jvG4ft20vDCfVbGg8pdhhFClShjJlMl2
    - your_second_api_key_here
    - your_third_api_key_here
  # 每个key的月使用限制（默认500次）
  monthly-limit: 500
  # 是否启用key轮换
  rotation-enabled: true
```

### 配置参数说明
- `api-keys`: API密钥列表，可以配置多个key
- `monthly-limit`: 每个key的月使用限制，默认500次
- `rotation-enabled`: 是否启用key轮换，默认true

## API接口

### 1. 图片压缩（自动使用可用key）
```
POST /api/image/compress
```
- 自动选择可用的API key进行压缩
- 支持缓存功能，避免重复调用API

### 2. 获取API key使用统计
```
GET /api/image/tinypng/stats
```
返回示例：
```
=== TinyPNG API Key 使用统计 ===
Key 1 (jvG4****lMl2): 45/500 次 (9.0%)
Key 2 (abcd****xyz9): 0/500 次 (0.0%)
Key 3 (efgh****123a): 0/500 次 (0.0%)
当前月份: 2025-01
轮换启用: 是
```

### 3. 检查可用key状态
```
GET /api/image/tinypng/available
```
- 返回是否还有可用的API key

### 4. 重置使用统计
```
POST /api/image/tinypng/reset-stats
```
- 重置当前月份的所有key使用统计

## 使用方式

### 1. 添加多个API key
在 `application.yml` 中配置多个key：
```yaml
tinypng:
  api-keys:
    - your_first_api_key
    - your_second_api_key
    - your_third_api_key
```

### 2. 监控使用情况
```bash
# 查看使用统计
curl http://localhost:8090/api/image/tinypng/stats

# 检查是否有可用key
curl http://localhost:8090/api/image/tinypng/available
```

### 3. 压缩图片
```bash
# 上传图片进行压缩（自动选择可用key）
curl -X POST -F "file=@image.jpg" http://localhost:8090/api/image/compress
```

## 工作原理

### 1. Key轮换策略
- 系统维护一个当前key索引
- 每次压缩时，从当前索引开始查找可用key
- 找到可用key后，更新索引到下一个位置
- 如果当前key已达到限制，自动跳到下一个

### 2. 使用量统计
- 使用 "apiKey-YYYY-MM" 格式作为统计键
- 每次API调用后自动增加使用计数
- 每月自动重置统计（基于当前日期）

### 3. 限制检查
- 每次获取key前检查当前月使用量
- 达到90%时发出警告日志
- 达到100%时跳过该key

## 日志监控

### 关键日志信息
```
# 初始化日志
TinyPNG配置初始化完成:
- 配置了 3 个API keys
- 月使用限制: 500 次/key
- 轮换启用: 是

# 使用日志
使用API key索引: 0, 本月已使用: 45/500

# 警告日志
API key使用量警告: 本月已使用 450/500 次

# 错误日志
所有TinyPNG API keys都已达到月使用限制 (500次)
```

## 性能优势

### 1. 扩展使用量
- 单个key: 500次/月
- 3个key: 1500次/月
- N个key: N×500次/月

### 2. 高可用性
- 单个key失效不影响服务
- 自动故障转移
- 优雅降级（使用原图）

### 3. 成本优化
- 充分利用每个账号的免费额度
- 避免不必要的付费升级
- 智能使用量分配

## 注意事项

1. **API Key安全**: 不要在日志中暴露完整的API key
2. **月度重置**: 统计会在每月自动重置，无需手动操作
3. **网络依赖**: 首次压缩仍需要网络访问TinyPNG API
4. **缓存优先**: 系统优先使用缓存，减少API调用
5. **配置验证**: 启动时会验证API key配置的有效性

## 故障排除

### 1. 所有key都达到限制
- 检查当前月份的使用统计
- 考虑添加更多API key
- 等待下月自动重置

### 2. Key轮换不生效
- 检查 `rotation-enabled` 配置
- 确认配置了多个有效的API key
- 查看应用启动日志

### 3. 使用统计异常
- 手动重置当前月统计
- 检查系统时间是否正确
- 查看相关错误日志

## 扩展建议

1. **监控告警**: 集成监控系统，在使用量达到阈值时发送告警
2. **自动续费**: 考虑集成付费API的自动续费机制
3. **负载均衡**: 根据key的剩余额度智能分配请求
4. **统计持久化**: 将使用统计保存到数据库，避免重启丢失
